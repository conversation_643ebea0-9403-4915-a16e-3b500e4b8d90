# Enhanced Error Logging for Contact Form

This document outlines the comprehensive error logging system implemented to help diagnose and fix contact form submission issues.

## Features Added

### 1. Server-Side Logging Enhancements

#### Contact Form Route (`server/routes.ts`)
- **Request ID tracking**: Each request gets a unique ID for tracing
- **Detailed step-by-step logging**: Logs every step of the submission process
- **Enhanced error details**: Captures error type, stack trace, database codes, etc.
- **Database health check**: Verifies database connectivity before processing
- **Intelligent error categorization**: Different status codes based on error type

#### Database Layer (`server/storage.ts`)
- **Database operation logging**: Logs all database operations with context
- **Error details capture**: Captures database-specific error information
- **Health check method**: New method to test database connectivity

#### Turnstile Verification (`server/turnstile.ts`)
- **Verification ID tracking**: Each verification gets a unique ID
- **Cloudflare API logging**: Logs requests and responses to/from Cloudflare
- **Detailed error reporting**: Captures API response details and error codes

#### Email System (`server/email.ts`)
- **Email ID tracking**: Each email operation gets a unique ID
- **SMTP operation logging**: Logs email sending attempts and results
- **Error categorization**: Captures SMTP-specific error details

### 2. Client-Side Logging Enhancements

#### API Request Layer (`client/src/lib/queryClient.ts`)
- **Request/response logging**: Logs all API requests and responses
- **Enhanced error objects**: Attaches response details to error objects
- **Request ID correlation**: Matches client requests with server logs

#### Contact Form Component (`client/src/components/contact-section.tsx`)
- **Form submission tracking**: Logs form submission attempts with context
- **Enhanced error handling**: Captures and logs detailed error information
- **User-friendly error messages**: Shows appropriate messages based on error type

#### Error Reporter (`client/src/lib/error-reporter.ts`)
- **Global error handling**: Captures unhandled JavaScript errors and promise rejections
- **Local storage**: Stores error reports locally for debugging
- **System information**: Captures browser and system details
- **Contact form specific reporting**: Special handling for contact form errors

### 3. Debug Tools

#### Debug Panel (`client/src/components/debug-panel.tsx`)
- **Visual debug interface**: Shows error reports and system status
- **System status monitoring**: Real-time system health checks
- **Error report viewer**: Browse and copy error reports
- **Copy functionality**: Easy copying of debug information

#### Debug Utilities (`client/src/lib/debug-utils.ts`)
- **Console commands**: Easy debug mode control via browser console
- **Error testing**: Ability to trigger test errors
- **System information**: Quick access to system details

#### System Status Endpoint (`/api/system-status`)
- **Comprehensive health check**: Tests database, SMTP, Turnstile, environment
- **Environment validation**: Checks for required environment variables
- **Detailed status reporting**: Provides actionable information for debugging

## How to Use

### For Development
1. Debug panel is automatically visible in development mode
2. All error logs appear in browser console and server logs
3. Use `debugUtils` commands in browser console for testing

### For Production Debugging
1. Enable debug mode: Open browser console and run `debugUtils.enable()`
2. Refresh the page to see the debug panel
3. Reproduce the error
4. Use debug panel to view error reports and system status
5. Copy relevant information for analysis

### Console Commands
```javascript
// Enable debug mode
debugUtils.enable()

// Disable debug mode
debugUtils.disable()

// Clear stored error reports
debugUtils.clearErrors()

// View stored error reports
debugUtils.getErrors()

// Get system information
debugUtils.systemInfo()

// Test error reporting
debugUtils.testError()
```

### API Endpoints for Debugging
- `GET /api/health` - Basic health check
- `GET /api/system-status` - Detailed system status

## Error Information Captured

### Server-Side
- Request ID for correlation
- Client IP and User-Agent
- Form data structure (sanitized)
- Database operation details
- SMTP configuration and errors
- Turnstile verification details
- Environment variable status
- Stack traces and error codes

### Client-Side
- Browser and system information
- Network connection details
- Form submission context
- API request/response details
- JavaScript errors and promise rejections
- User interaction context

## Troubleshooting Common Issues

### 500 Internal Server Error
1. Check server logs for request ID
2. Look for database connection errors
3. Verify environment variables are set
4. Check SMTP configuration if email-related

### Turnstile Verification Failures
1. Check Turnstile configuration in logs
2. Verify TURNSTILE_SECRET_KEY is set
3. Look for Cloudflare API response details

### Database Connection Issues
1. Check database health in system status
2. Verify DATABASE_URL environment variable
3. Look for connection timeout or authentication errors

### Email Sending Problems
1. Check SMTP configuration in system status
2. Verify email environment variables
3. Look for SMTP authentication or connection errors

## Log Correlation

Each request generates a unique request ID that appears in:
- Server logs (all components)
- Client error reports
- API responses
- Debug panel information

Use this ID to trace a specific request through the entire system.

## Security Considerations

- Sensitive data (passwords, tokens) is automatically redacted from logs
- Error reports exclude actual form data, only structure information
- Debug panel only shows in development or when explicitly enabled
- System status endpoint doesn't expose sensitive configuration details
