# Deployment Configuration Guide

## Vercel Deployment

### Environment Variables Required:
```
DATABASE_URL=postgresql://username:password@host:port/database
NODE_ENV=production
```

### Build Configuration:
- Build Command: `npm run build`
- Output Directory: `dist/public`
- Install Command: `npm install`

### Notes:
- The application serves both API and static files from the same server
- Database connections are optimized for serverless environments
- Static assets are served from `dist/public` after build

## Coolify Deployment

### Environment Variables Required:
```
DATABASE_URL=postgresql://username:password@host:port/database
NODE_ENV=production
PORT=3000
```

### Docker Configuration:
- Uses the included Dockerfile for containerized deployment
- Health check endpoint: `/api/health`
- Default port: 3000 (configurable via PORT env var)

### Build Process:
1. Frontend build: Creates optimized React bundle
2. Backend build: Bundles server code with esbuild
3. Static serving: Express serves frontend from `dist/public`

## Common Issues and Solutions:

### Database Connection Issues:
- Ensure DATABASE_URL is properly formatted
- For Neon/serverless databases, connection pooling is optimized
- Health check endpoint tests database connectivity

### Port Configuration:
- Development: Port 5000 (default)
- Production: Port 3000 (default) or PORT environment variable

### Build Failures:
- Ensure all dependencies are properly installed
- TypeScript compilation must pass before build
- Database migrations should be run separately: `npm run db:push`