import bcrypt from 'bcryptjs';
import { randomBytes } from 'crypto';
import { db } from './db';
import { users, sessions } from '@shared/schema';
import { eq, and, gt } from 'drizzle-orm';
import type { User, Session, LoginData } from '@shared/schema';

// Session duration (7 days)
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000;

// Generate a secure session ID
function generateSessionId(): string {
  return randomBytes(32).toString('hex');
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Create a new session
export async function createSession(userId: number): Promise<string> {
  const sessionId = generateSessionId();
  const expiresAt = new Date(Date.now() + SESSION_DURATION);

  await db.insert(sessions).values({
    id: sessionId,
    userId,
    expiresAt,
  });

  return sessionId;
}

// Get session with user data
export async function getSessionWithUser(sessionId: string): Promise<{ user: User; session: Session } | null> {
  const result = await db
    .select({
      user: users,
      session: sessions,
    })
    .from(sessions)
    .innerJoin(users, eq(sessions.userId, users.id))
    .where(
      and(
        eq(sessions.id, sessionId),
        gt(sessions.expiresAt, new Date()),
        eq(users.isActive, true)
      )
    )
    .limit(1);

  return result[0] || null;
}

// Delete session (logout)
export async function deleteSession(sessionId: string): Promise<void> {
  await db.delete(sessions).where(eq(sessions.id, sessionId));
}

// Clean up expired sessions
export async function cleanupExpiredSessions(): Promise<void> {
  await db.delete(sessions).where(gt(new Date(), sessions.expiresAt));
}

// Authenticate user
export async function authenticateUser(loginData: LoginData): Promise<User | null> {
  const [user] = await db
    .select()
    .from(users)
    .where(
      and(
        eq(users.username, loginData.username),
        eq(users.isActive, true)
      )
    )
    .limit(1);

  if (!user) {
    return null;
  }

  const isValidPassword = await verifyPassword(loginData.password, user.password);
  if (!isValidPassword) {
    return null;
  }

  // Update last login time
  await db
    .update(users)
    .set({ lastLoginAt: new Date() })
    .where(eq(users.id, user.id));

  return user;
}

// Create admin user (for initial setup)
export async function createAdminUser(userData: {
  username: string;
  password: string;
  email: string;
  firstName: string;
  lastName: string;
}): Promise<User> {
  const hashedPassword = await hashPassword(userData.password);

  const [user] = await db
    .insert(users)
    .values({
      ...userData,
      password: hashedPassword,
      role: 'admin',
    })
    .returning();

  return user;
}

// Check if any admin users exist
export async function hasAdminUsers(): Promise<boolean> {
  const [result] = await db
    .select({ count: users.id })
    .from(users)
    .where(
      and(
        eq(users.role, 'admin'),
        eq(users.isActive, true)
      )
    )
    .limit(1);

  return !!result;
}

// Middleware to check authentication
export function requireAuth(req: any, res: any, next: any) {
  const sessionId = req.cookies?.sessionId;
  
  if (!sessionId) {
    return res.status(401).json({ 
      success: false, 
      message: 'Authentication required' 
    });
  }

  // Verify session in the actual route handler
  next();
}

// Middleware to check admin role
export function requireAdmin(req: any, res: any, next: any) {
  // This will be used after authentication is verified
  if (req.user?.role !== 'admin') {
    return res.status(403).json({ 
      success: false, 
      message: 'Admin access required' 
    });
  }
  next();
}
