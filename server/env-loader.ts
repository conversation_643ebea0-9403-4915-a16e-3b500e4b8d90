// Environment variable loader and validator
import { z } from 'zod';

// Environment schema for validation
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  DATABASE_URL: z.string().optional(),
  PORT: z.string().optional(),
  HOST: z.string().default('0.0.0.0'),

  // Email Configuration
  // EmailIt API Configuration
  EMAILIT_API_KEY: z.string().optional(),
  FROM_NAME: z.string().optional(),
  FROM_EMAIL: z.string().optional(),

  // Legacy SMTP Configuration (optional, for backward compatibility)
  SMTP_SERVER: z.string().optional(),
  SMTP_PORT: z.string().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  TLS: z.string().optional(),

  // Security
  SESSION_SECRET: z.string().optional(),
  TURNSTILE_SECRET_KEY: z.string().optional(),
  VITE_TURNSTILE_SITE_KEY: z.string().optional(),
});

// Validate environment variables
export function validateEnvironment() {
  try {
    const parsed = envSchema.parse(process.env);

    // Set defaults for development
    const env = {
      ...parsed,
      DATABASE_URL: parsed.DATABASE_URL || 'postgresql://localhost:5432/spot_dev',
      SESSION_SECRET: parsed.SESSION_SECRET || 'dev-session-secret-key-for-local-development-only-change-in-production',
    };

    // Validate required fields for production
    if (env.NODE_ENV === 'production') {
      if (!process.env.DATABASE_URL) {
        throw new Error('DATABASE_URL is required in production');
      }
      if (!process.env.SESSION_SECRET || process.env.SESSION_SECRET.length < 32) {
        throw new Error('SESSION_SECRET must be at least 32 characters in production');
      }
    }

    console.log('✅ Environment variables validated successfully');
    if (env.NODE_ENV === 'development' && !process.env.DATABASE_URL) {
      console.log('⚠️  Using default DATABASE_URL for development. Set DATABASE_URL env var to connect to your database.');
    }

    return env;
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
    throw error;
  }
}

// Initialize environment validation
export const env = validateEnvironment();