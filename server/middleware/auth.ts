import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Simple API key authentication for admin endpoints
// In production, this should be replaced with proper JWT or session-based auth
const API_KEY_HEADER = 'x-api-key';

export interface AuthenticatedRequest extends Request {
  isAuthenticated?: boolean;
  user?: {
    id: string;
    role: string;
  };
}

// Middleware to check API key for admin endpoints
export function requireApiKey(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  const apiKey = req.headers[API_KEY_HEADER] as string;
  
  // In production, this should be a secure API key from environment variables
  const validApiKey = process.env.ADMIN_API_KEY;
  
  if (!validApiKey) {
    return res.status(503).json({ 
      message: 'Admin functionality not configured' 
    });
  }
  
  if (!apiKey || apiKey !== validApiKey) {
    return res.status(401).json({ 
      message: 'Unauthorized: Valid API key required' 
    });
  }
  
  req.isAuthenticated = true;
  req.user = { id: 'admin', role: 'admin' };
  next();
}

// Rate limiting middleware
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(windowMs: number = 15 * 60 * 1000, maxRequests: number = 100) {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    
    // Clean up expired entries
    for (const [ip, data] of rateLimitStore.entries()) {
      if (now > data.resetTime) {
        rateLimitStore.delete(ip);
      }
    }
    
    const clientData = rateLimitStore.get(clientIP);
    
    if (!clientData) {
      rateLimitStore.set(clientIP, { count: 1, resetTime: now + windowMs });
      return next();
    }
    
    if (now > clientData.resetTime) {
      rateLimitStore.set(clientIP, { count: 1, resetTime: now + windowMs });
      return next();
    }
    
    if (clientData.count >= maxRequests) {
      return res.status(429).json({
        message: 'Too many requests, please try again later',
        retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
      });
    }
    
    clientData.count++;
    next();
  };
}

// Input sanitization middleware
export function sanitizeInput(req: Request, res: Response, next: NextFunction) {
  // Basic XSS protection - strip HTML tags from string inputs
  function sanitizeValue(value: any): any {
    if (typeof value === 'string') {
      return value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                  .replace(/<[^>]*>/g, '')
                  .trim();
    }
    if (typeof value === 'object' && value !== null) {
      const sanitized: any = Array.isArray(value) ? [] : {};
      for (const key in value) {
        sanitized[key] = sanitizeValue(value[key]);
      }
      return sanitized;
    }
    return value;
  }
  
  if (req.body) {
    req.body = sanitizeValue(req.body);
  }
  
  next();
}

// Validation middleware factory
export function validateSchema<T>(schema: z.ZodSchema<T>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: 'Validation error',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
}
