interface LogContext {
  [key: string]: any;
}

export class Logger {
  private static formatTimestamp(): string {
    return new Date().toISOString();
  }

  private static formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = this.formatTimestamp();
    const contextStr = context ? ` ${JSON.stringify(context)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${contextStr}`;
  }

  static info(message: string, context?: LogContext): void {
    console.log(this.formatMessage('info', message, context));
  }

  static warn(message: string, context?: LogContext): void {
    console.warn(this.formatMessage('warn', message, context));
  }

  static error(message: string, context?: LogContext): void {
    console.error(this.formatMessage('error', message, context));
  }

  static debug(message: string, context?: LogContext): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(this.formatMessage('debug', message, context));
    }
  }

  // Create a scoped logger with a prefix
  static scope(prefix: string) {
    return {
      info: (message: string, context?: LogContext) => 
        Logger.info(`[${prefix}] ${message}`, context),
      warn: (message: string, context?: LogContext) => 
        Logger.warn(`[${prefix}] ${message}`, context),
      error: (message: string, context?: LogContext) => 
        Logger.error(`[${prefix}] ${message}`, context),
      debug: (message: string, context?: LogContext) => 
        Logger.debug(`[${prefix}] ${message}`, context),
    };
  }
}

// Helper function to extract error details
export function getErrorDetails(error: any): LogContext {
  return {
    message: error.message,
    name: error.name,
    stack: error.stack,
    code: error.code,
    detail: error.detail,
    constraint: error.constraint,
    table: error.table,
    column: error.column,
    dataType: error.dataType,
    cause: error.cause,
    response: error.response,
    responseCode: error.responseCode,
    command: error.command,
    timestamp: new Date().toISOString()
  };
}

// Helper function to sanitize sensitive data for logging
export function sanitizeForLogging(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  const sanitized = { ...data };

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  // Recursively sanitize nested objects
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeForLogging(sanitized[key]);
    }
  }

  return sanitized;
}
