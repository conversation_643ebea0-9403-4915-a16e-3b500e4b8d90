interface TurnstileResponse {
  success: boolean;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

export async function verifyTurnstile(token: string, ip?: string): Promise<boolean> {
  const verificationId = Date.now().toString(36) + Math.random().toString(36).substr(2);

  console.log(`[${verificationId}] Starting Turnstile verification`, {
    tokenLength: token?.length || 0,
    hasIP: !!ip,
    ip: ip,
    timestamp: new Date().toISOString()
  });

  if (!process.env.TURNSTILE_SECRET_KEY) {
    console.error(`[${verificationId}] TURNSTILE_SECRET_KEY not configured`);
    return false;
  }

  try {
    const formData = new FormData();
    formData.append('secret', process.env.TURNSTILE_SECRET_KEY);
    formData.append('response', token);
    if (ip) {
      formData.append('remoteip', ip);
    }

    console.log(`[${verificationId}] Sending verification request to Cloudflare`);

    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      body: formData,
    });

    console.log(`[${verificationId}] Received response from Cloudflare`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      console.error(`[${verificationId}] Cloudflare API returned non-OK status:`, {
        status: response.status,
        statusText: response.statusText
      });
      return false;
    }

    const data: TurnstileResponse = await response.json();

    console.log(`[${verificationId}] Turnstile verification response:`, {
      success: data.success,
      challenge_ts: data.challenge_ts,
      hostname: data.hostname,
      errorCodes: data['error-codes']
    });

    if (!data.success) {
      console.error(`[${verificationId}] Turnstile verification failed:`, {
        errorCodes: data['error-codes'],
        hostname: data.hostname,
        challenge_ts: data.challenge_ts
      });
      return false;
    }

    console.log(`[${verificationId}] Turnstile verification successful`);
    return true;
  } catch (error) {
    console.error(`[${verificationId}] Error verifying Turnstile:`, {
      error: error.message,
      stack: error.stack,
      name: error.name,
      cause: error.cause,
      timestamp: new Date().toISOString()
    });
    return false;
  }
}