import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import {
  insertContactSubmissionSchema,
  insertPostSchema,
  insertCategorySchema,
  insertCommentSchema,
  insertPageSchema
} from "@shared/schema";
import { sendContactAcknowledgement, sendContactNotification } from "./email";
import { verifyTurnstile } from "./turnstile";
import { requireApiKey, sanitizeInput, validateSchema } from "./middleware/auth";
import {
  authenticateUser,
  createSession,
  getSessionWithUser,
  deleteSession,
  hasAdminUsers,
  createAdminUser,
  requireAuth
} from "./auth";
import { loginSchema } from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Simple test endpoint (no dependencies)
  app.get("/api/test", (req, res) => {
    res.status(200).json({
      status: "ok",
      timestamp: new Date().toISOString(),
      message: "Serverless function is working"
    });
  });

  // Simple health check endpoint (no dependencies)
  app.get("/api/health", async (req, res) => {
    res.status(200).json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "SPOT Platform",
      environment: process.env.NODE_ENV || 'unknown'
    });
  });

  // Database health check endpoint
  app.get("/api/health/database", async (req, res) => {
    try {
      // Test database connection
      await storage.getContactSubmissions();
      res.status(200).json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        service: "Database"
      });
    } catch (error) {
      console.error("Database health check failed:", error);
      res.status(503).json({
        status: "unhealthy",
        error: "Database connection failed",
        timestamp: new Date().toISOString()
      });
    }
  });

  // Detailed system status endpoint for debugging
  app.get("/api/system-status", async (req, res) => {
    const status = {
      timestamp: new Date().toISOString(),
      service: "SPOT Platform",
      environment: process.env.NODE_ENV || 'unknown',
      checks: {
        database: { status: 'unknown', details: null },
        email: { status: 'unknown', details: null },
        turnstile: { status: 'unknown', details: null },
        environment: { status: 'unknown', details: null }
      }
    };

    // Database check
    try {
      const dbHealthy = await storage.healthCheck();
      status.checks.database = {
        status: dbHealthy ? 'healthy' : 'unhealthy',
        details: dbHealthy ? 'Connection successful' : 'Connection failed'
      };
    } catch (error) {
      status.checks.database = {
        status: 'error',
        details: error.message
      };
    }

    // Email service check (EmailIt) - simplified to avoid serverless issues
    try {
      status.checks.email = {
        status: process.env.EMAILIT_API_KEY ? 'configured' : 'not_configured',
        details: {
          service: 'EmailIt API',
          apiKey: process.env.EMAILIT_API_KEY ? '[CONFIGURED]' : 'Not set',
          fromEmail: process.env.FROM_EMAIL || '<EMAIL> (default)',
          fromName: process.env.FROM_NAME || 'SPOT - Assessment Made Easy (default)'
        }
      };
    } catch (error) {
      status.checks.email = {
        status: 'error',
        details: {
          service: 'EmailIt API',
          error: error.message,
          apiKey: process.env.EMAILIT_API_KEY ? '[CONFIGURED]' : 'Not set'
        }
      };
    }

    // Turnstile check
    status.checks.turnstile = {
      status: process.env.TURNSTILE_SECRET_KEY ? 'configured' : 'not_configured',
      details: {
        secretKey: process.env.TURNSTILE_SECRET_KEY ? '[CONFIGURED]' : 'Not set'
      }
    };

    // Environment variables check
    const requiredEnvVars = ['DATABASE_URL', 'TURNSTILE_SECRET_KEY', 'EMAILIT_API_KEY'];
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

    status.checks.environment = {
      status: missingEnvVars.length === 0 ? 'healthy' : 'missing_variables',
      details: {
        missing: missingEnvVars,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };

    const overallHealthy = Object.values(status.checks).every(check =>
      ['healthy', 'configured'].includes(check.status)
    );

    res.status(overallHealthy ? 200 : 503).json(status);
  });

  // Authentication Routes

  // Check if setup is needed
  app.get("/api/auth/setup-needed", async (req, res) => {
    try {
      const needsSetup = !(await hasAdminUsers());
      res.json({ needsSetup });
    } catch (error) {
      console.error("Error checking setup status:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Initial admin setup
  app.post("/api/auth/setup", sanitizeInput, async (req, res) => {
    try {
      // Check if setup is still needed
      if (await hasAdminUsers()) {
        return res.status(400).json({
          success: false,
          message: "Setup already completed"
        });
      }

      const { username, password, email, firstName, lastName } = req.body;

      if (!username || !password || !email || !firstName || !lastName) {
        return res.status(400).json({
          success: false,
          message: "All fields are required"
        });
      }

      const user = await createAdminUser({
        username,
        password,
        email,
        firstName,
        lastName
      });

      res.json({
        success: true,
        message: "Admin user created successfully",
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role
        }
      });
    } catch (error) {
      console.error("Error during setup:", error);
      res.status(500).json({
        success: false,
        message: "Error creating admin user"
      });
    }
  });

  // Login
  app.post("/api/auth/login", sanitizeInput, validateSchema(loginSchema), async (req, res) => {
    try {
      const user = await authenticateUser(req.body);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: "Invalid username or password"
        });
      }

      const sessionId = await createSession(user.id);

      // Set secure cookie
      res.cookie('sessionId', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      res.json({
        success: true,
        message: "Login successful",
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role
        }
      });
    } catch (error) {
      console.error("Error during login:", error);
      res.status(500).json({
        success: false,
        message: "Login failed"
      });
    }
  });

  // Check authentication status
  app.get("/api/auth/me", async (req, res) => {
    try {
      const sessionId = req.cookies?.sessionId;

      if (!sessionId) {
        return res.status(401).json({
          success: false,
          message: "Not authenticated"
        });
      }

      const sessionData = await getSessionWithUser(sessionId);

      if (!sessionData) {
        return res.status(401).json({
          success: false,
          message: "Invalid session"
        });
      }

      res.json({
        success: true,
        user: {
          id: sessionData.user.id,
          username: sessionData.user.username,
          email: sessionData.user.email,
          firstName: sessionData.user.firstName,
          lastName: sessionData.user.lastName,
          role: sessionData.user.role
        }
      });
    } catch (error) {
      console.error("Error checking authentication:", error);
      res.status(500).json({
        success: false,
        message: "Authentication check failed"
      });
    }
  });

  // Logout
  app.post("/api/auth/logout", async (req, res) => {
    try {
      const sessionId = req.cookies?.sessionId;

      if (sessionId) {
        await deleteSession(sessionId);
      }

      res.clearCookie('sessionId');
      res.json({
        success: true,
        message: "Logout successful"
      });
    } catch (error) {
      console.error("Error during logout:", error);
      res.status(500).json({
        success: false,
        message: "Logout failed"
      });
    }
  });

  // Simple contact form test endpoint
  app.post("/api/contact-test", (req, res) => {
    console.log('Contact form test endpoint called');
    res.json({
      success: true,
      message: "Test endpoint working",
      timestamp: new Date().toISOString()
    });
  });

  // Contact form submission endpoint with email and Turnstile verification
  app.post("/api/contact", sanitizeInput, validateSchema(insertContactSubmissionSchema.extend({
    turnstileToken: z.string().min(1, "Turnstile token is required")
  })), async (req, res) => {
    const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2);

    console.log(`[${requestId}] Contact form submission started`);

    try {
      const { turnstileToken, ...formData } = req.body;

      // Verify Turnstile token
      if (!turnstileToken) {
        return res.status(400).json({
          success: false,
          message: "Bot verification is required. Please try again.",
          requestId
        });
      }

      const clientIP = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
      const turnstileValid = await verifyTurnstile(
        turnstileToken,
        Array.isArray(clientIP) ? clientIP[0] : clientIP?.toString()
      );

      if (!turnstileValid) {
        return res.status(400).json({
          success: false,
          message: "Bot verification failed. Please refresh and try again.",
          requestId
        });
      }

      // Validate form data
      const validatedData = insertContactSubmissionSchema.parse(formData);

      // Save to database
      const submission = await storage.createContactSubmission(validatedData);

      // Send emails asynchronously (don't wait for completion)
      Promise.all([
        sendContactAcknowledgement(validatedData),
        sendContactNotification(validatedData)
      ]).catch(error => {
        console.error(`[${requestId}] Email sending error:`, error.message);
      });

      console.log(`[${requestId}] Contact form submission completed successfully`);

      res.json({
        success: true,
        message: "Thank you for your interest! We will contact you shortly to schedule your demo. Please check your email for confirmation.",
        id: submission.id,
        requestId
      });
    } catch (error) {
      console.error(`[${requestId}] Contact form submission error:`, error.message);

      res.status(500).json({
        success: false,
        message: "There was an error submitting your request. Please try again.",
        requestId,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Get all contact submissions (for admin purposes) - PROTECTED
  app.get("/api/admin/contact-submissions", async (req, res) => {
    try {
      const sessionId = req.cookies?.sessionId;

      if (!sessionId) {
        return res.status(401).json({
          success: false,
          message: "Authentication required"
        });
      }

      const sessionData = await getSessionWithUser(sessionId);

      if (!sessionData || sessionData.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: "Admin access required"
        });
      }

      const submissions = await storage.getContactSubmissions();
      res.json({
        success: true,
        data: submissions
      });
    } catch (error) {
      console.error("Error fetching contact submissions:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error"
      });
    }
  });

  // Legacy API key protected endpoint (for backward compatibility)
  app.get("/api/contact-submissions", requireApiKey, async (req, res) => {
    try {
      const submissions = await storage.getContactSubmissions();
      res.json(submissions);
    } catch (error) {
      console.error("Error fetching contact submissions:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Blog Posts API
  app.get("/api/posts", async (req, res) => {
    try {
      const published = req.query.published === 'true';
      const posts = await storage.getPosts(published);
      res.json(posts);
    } catch (error) {
      console.error("Error fetching posts:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/api/posts/:slug", async (req, res) => {
    try {
      const post = await storage.getPost(req.params.slug);
      if (!post) {
        return res.status(404).json({ message: "Post not found" });
      }
      res.json(post);
    } catch (error) {
      console.error("Error fetching post:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.post("/api/posts", requireApiKey, sanitizeInput, validateSchema(insertPostSchema), async (req, res) => {
    try {
      const post = await storage.createPost(req.body);
      res.json(post);
    } catch (error) {
      console.error("Error creating post:", error);
      res.status(400).json({ message: "Invalid post data" });
    }
  });

  app.put("/api/posts/:id", requireApiKey, sanitizeInput, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const post = await storage.updatePost(id, req.body);
      res.json(post);
    } catch (error) {
      console.error("Error updating post:", error);
      res.status(400).json({ message: "Error updating post" });
    }
  });

  app.delete("/api/posts/:id", requireApiKey, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deletePost(id);
      res.json({ success: true });
    } catch (error) {
      console.error("Error deleting post:", error);
      res.status(400).json({ message: "Error deleting post" });
    }
  });

  // Categories API
  app.get("/api/categories", async (req, res) => {
    try {
      const categories = await storage.getCategories();
      res.json(categories);
    } catch (error) {
      console.error("Error fetching categories:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.post("/api/categories", requireApiKey, sanitizeInput, validateSchema(insertCategorySchema), async (req, res) => {
    try {
      const category = await storage.createCategory(req.body);
      res.json(category);
    } catch (error) {
      console.error("Error creating category:", error);
      res.status(400).json({ message: "Invalid category data" });
    }
  });

  // Comments API
  app.get("/api/posts/:postId/comments", async (req, res) => {
    try {
      const postId = parseInt(req.params.postId);
      const comments = await storage.getComments(postId);
      res.json(comments);
    } catch (error) {
      console.error("Error fetching comments:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.post("/api/posts/:postId/comments", async (req, res) => {
    try {
      const postId = parseInt(req.params.postId);
      const validatedData = insertCommentSchema.parse({
        ...req.body,
        postId
      });
      const comment = await storage.createComment(validatedData);
      res.json(comment);
    } catch (error) {
      console.error("Error creating comment:", error);
      res.status(400).json({ message: "Invalid comment data" });
    }
  });

  app.put("/api/comments/:id/status", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;
      const comment = await storage.updateCommentStatus(id, status);
      res.json(comment);
    } catch (error) {
      console.error("Error updating comment status:", error);
      res.status(400).json({ message: "Error updating comment" });
    }
  });

  // Pages API
  app.get("/api/pages", async (req, res) => {
    try {
      const pages = await storage.getPages();
      res.json(pages);
    } catch (error) {
      console.error("Error fetching pages:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/api/pages/:slug", async (req, res) => {
    try {
      const page = await storage.getPage(req.params.slug);
      if (!page) {
        return res.status(404).json({ message: "Page not found" });
      }
      res.json(page);
    } catch (error) {
      console.error("Error fetching page:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.post("/api/pages", async (req, res) => {
    try {
      const validatedData = insertPageSchema.parse(req.body);
      const page = await storage.createPage(validatedData);
      res.json(page);
    } catch (error) {
      console.error("Error creating page:", error);
      res.status(400).json({ message: "Invalid page data" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
