# SPOT Platform - Security Guide

## 🔒 Security Features Implemented

### 1. Environment Security
- **Environment Validation**: All environment variables are validated using Zod schemas
- **No Hardcoded Secrets**: All sensitive credentials must be provided via environment variables
- **Secure Defaults**: Production-safe defaults for all security settings

### 2. API Security
- **Authentication**: Admin endpoints protected with API key authentication
- **Rate Limiting**: 100 requests per 15 minutes per IP address
- **Input Validation**: All inputs validated using Zod schemas
- **Input Sanitization**: XSS protection through HTML tag stripping
- **CORS Configuration**: Properly configured CORS with allowed origins

### 3. Security Headers
- **X-Frame-Options**: Prevents clickjacking attacks
- **X-Content-Type-Options**: Prevents MIME type sniffing
- **X-XSS-Protection**: Enables browser XSS protection
- **Content-Security-Policy**: Restricts resource loading
- **Strict-Transport-Security**: Enforces HTTPS connections
- **Referrer-Policy**: Controls referrer information

### 4. Database Security
- **SQL Injection Protection**: Using Drizzle ORM with parameterized queries
- **Connection Pooling**: Secure connection management
- **Environment-based Configuration**: No hardcoded database credentials

### 5. Email Security
- **TLS Configuration**: Proper TLS settings for email transport
- **Certificate Validation**: Enabled in production environments
- **Graceful Degradation**: Email functionality disabled if not configured

## 🚀 Deployment Security

### Vercel Deployment
- **Server-side Functions**: Properly configured for API endpoints
- **Environment Variables**: Secure handling of secrets
- **Build Optimization**: Separate builds for client and server

### Coolify/Docker Deployment
- **Multi-stage Builds**: Reduced attack surface
- **Non-root User**: Application runs as non-privileged user
- **Security Updates**: Base images updated with security patches
- **Health Checks**: Proper health monitoring

## 🔧 Required Environment Variables

### Production Environment
```bash
# Database (Required)
DATABASE_URL=postgresql://username:password@host:port/database

# Security (Required)
SESSION_SECRET=your-32-character-minimum-session-secret
ADMIN_API_KEY=your-secure-admin-api-key

# SMTP (Optional)
SMTP_SERVER=your-smtp-server
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
FROM_EMAIL=your-from-email
FROM_NAME=your-from-name

# Turnstile (Optional)
TURNSTILE_SECRET_KEY=your-turnstile-secret
VITE_TURNSTILE_SITE_KEY=your-turnstile-site-key
```

## 🛡️ Security Best Practices

### 1. API Key Management
- Generate strong, random API keys (minimum 32 characters)
- Rotate API keys regularly
- Use different keys for different environments
- Never commit API keys to version control

### 2. Database Security
- Use strong database passwords
- Enable SSL/TLS for database connections
- Regularly backup database
- Monitor database access logs

### 3. HTTPS Configuration
- Always use HTTPS in production
- Configure proper SSL certificates
- Enable HSTS headers
- Redirect HTTP to HTTPS

### 4. Monitoring and Logging
- Monitor API usage and rate limits
- Log security events
- Set up alerts for suspicious activity
- Regular security audits

## 🚨 Security Incident Response

### If You Suspect a Security Issue:
1. **Immediate Actions**:
   - Rotate all API keys and secrets
   - Check access logs for suspicious activity
   - Temporarily disable affected endpoints if necessary

2. **Investigation**:
   - Review application logs
   - Check database for unauthorized access
   - Analyze network traffic

3. **Recovery**:
   - Apply security patches
   - Update all credentials
   - Notify affected users if necessary

## 📋 Security Checklist

### Before Deployment:
- [ ] All environment variables configured
- [ ] Strong passwords and API keys generated
- [ ] HTTPS properly configured
- [ ] Security headers tested
- [ ] Rate limiting verified
- [ ] Database access secured
- [ ] Email configuration tested
- [ ] Health checks working

### Regular Maintenance:
- [ ] Update dependencies monthly
- [ ] Rotate API keys quarterly
- [ ] Review access logs weekly
- [ ] Security audit annually
- [ ] Backup verification monthly

## 🔍 Security Testing

### Manual Testing:
```bash
# Test rate limiting
curl -X POST https://your-domain.com/api/contact -H "Content-Type: application/json" -d '{}' --repeat 101

# Test security headers
curl -I https://your-domain.com

# Test admin endpoint protection
curl https://your-domain.com/api/contact-submissions

# Test health endpoint
curl https://your-domain.com/api/health
```

### Automated Security Scanning:
- Use tools like `npm audit` for dependency vulnerabilities
- Implement SAST (Static Application Security Testing)
- Regular penetration testing
- Monitor with security scanning services

## 📞 Contact

For security-related questions or to report vulnerabilities:
- Email: <EMAIL>
- Create a private GitHub issue for sensitive matters

---

**Note**: This security guide should be reviewed and updated regularly as new threats emerge and security practices evolve.
