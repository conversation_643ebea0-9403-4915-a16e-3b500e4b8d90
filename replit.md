# SPOT - Student Proficiency Online Test Platform

## Overview

SPOT (Student Proficiency Online Test) is a full-stack web application designed for educational institutions to conduct online assessments. Built by MinervaInfo, it provides a comprehensive platform for creating, managing, and analyzing student assessments with a focus on Indian competitive exams like JEE, NEET, and UPSC.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side routing with blog and admin routes
- **UI Framework**: Shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with modern gradient designs
- **State Management**: React Query (TanStack Query) for server state
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion for smooth transitions

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Neon serverless driver
- **Database ORM**: Drizzle ORM with full CMS schema
- **Session Management**: Express sessions with PostgreSQL store
- **Validation**: Zod schemas for type-safe data validation

### CMS Architecture (Backend Infrastructure)
- **Blog Management**: Full CRUD operations for posts, categories, and comments (backend only)
- **Content Types**: Posts, Pages, Categories, Comments with rich metadata
- **User Authentication**: Ready for user registration and role-based access
- **Database Relations**: Comprehensive foreign key relationships
- **API Endpoints**: RESTful API for all CMS operations (accessible via direct URL)

### Build System
- **Frontend Bundler**: Vite with React plugin
- **Backend Bundler**: ESBuild for production builds
- **Development**: Hot module replacement with Vite dev server
- **TypeScript**: Shared types across client and server

## Key Components

### Database Schema
- **Users Table**: Authentication and user management
- **Contact Submissions Table**: Lead capture and demo requests
- **Extensible Design**: Ready for additional tables (tests, questions, results)

### API Structure
- **Contact API**: `/api/contact` for demo requests
- **Admin API**: `/api/contact-submissions` for viewing submissions
- **RESTful Design**: Prepared for expansion with test and user management endpoints

### Frontend Components
- **Landing Page**: Complete marketing site with sections for features, benefits, testimonials
- **Contact Form**: Lead capture with validation and success handling
- **Navigation**: Smooth scrolling single-page application
- **Responsive Design**: Mobile-first approach with Tailwind breakpoints

### Authentication System
- **Session-based**: Uses express-session with PostgreSQL storage
- **User Model**: Username/password authentication ready
- **Security**: Prepared for role-based access control

## Data Flow

1. **User Interaction**: Visitors interact with the React frontend
2. **Form Submission**: Contact forms are validated client-side with React Hook Form + Zod
3. **API Requests**: Data is sent to Express.js backend via fetch API
4. **Data Validation**: Server-side validation using shared Zod schemas
5. **Database Operations**: Drizzle ORM handles PostgreSQL interactions
6. **Response Handling**: Success/error states managed by React Query
7. **User Feedback**: Toast notifications provide user feedback

## External Dependencies

### Database
- **PostgreSQL**: Primary database (configured in Replit environment)
- **Neon Database**: Serverless PostgreSQL provider (@neondatabase/serverless)
- **Connection Pooling**: Built-in with Neon serverless driver

### UI Libraries
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library
- **Tailwind CSS**: Utility-first styling
- **Framer Motion**: Animation library

### Development Tools
- **Replit Integration**: Custom Vite plugins for Replit environment
- **Development Banner**: Replit development mode indicator
- **Hot Reload**: Full-stack development with automatic reload

## Deployment Strategy

### Development Environment
- **Platform**: Replit with Node.js 20 runtime
- **Database**: PostgreSQL 16 module
- **Port Configuration**: Frontend on port 5000, mapped to external port 80
- **Development Command**: `npm run dev` with hot reload

### Production Build
- **Frontend**: Vite builds to `dist/public` directory
- **Backend**: ESBuild bundles server to `dist/index.js`
- **Static Serving**: Express serves built frontend in production
- **Environment**: Production mode with `NODE_ENV=production`

### Scaling Considerations
- **Autoscale Deployment**: Configured for Replit's autoscale deployment target
- **Session Storage**: PostgreSQL-backed sessions for multi-instance deployment
- **Database**: Serverless PostgreSQL scales automatically
- **Static Assets**: Can be moved to CDN for better performance

## Recent Changes
- **June 17, 2025**: Deployment-Ready Configuration with Optional SMTP
  - Made SMTP configuration optional for production deployment to prevent crashes
  - Fixed port configuration to use 3000 for production, 5000 for development
  - Updated email functions to gracefully handle missing SMTP credentials
  - Enhanced error handling for deployment environments without email setup
  - Maintained development functionality with fallback SMTP credentials
- **June 17, 2025**: Enhanced Contact Form with SMTP and Security
  - Implemented SendGrid email integration for automated responses
  - Added Cloudflare Turnstile bot protection to contact form
  - Created professional email templates for user acknowledgements and team notifications
  - Enhanced hero section layout to match reference design proportions
  - Added floating success metrics card with user interaction
- **June 17, 2025**: CMS Infrastructure and Public Interface Separation
  - Removed public blog and CMS navigation links from main website
  - Kept full CMS backend infrastructure for future content management
  - Updated navigation to focus on core landing page sections (Home, Features, Benefits, About, Contact)
  - Maintained PostgreSQL database schema for posts, categories, comments, and user management
  - CMS functionality remains accessible via direct URLs for admin use
- **June 16, 2025**: Complete redesign and CMS implementation
  - Replaced old design with modern, clean aesthetic using gradients and improved typography
  - Implemented full-featured CMS with blog and comment management
  - Added PostgreSQL database with comprehensive schema

## Changelog
- June 16, 2025. Initial setup
- June 16, 2025. Major redesign and CMS implementation

## User Preferences

Preferred communication style: Simple, everyday language.
Requests: Modern, minimalistic design with CMS functionality.