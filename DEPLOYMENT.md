# SPOT Platform - Deployment Guide

## 🚀 Deployment Options

This guide covers deployment to both **Vercel** (serverless) and **Coolify** (containerized) platforms.

## Prerequisites

1. **PostgreSQL Database**: Ensure you have a PostgreSQL database URL available
2. **Environment Variables**: Configure the required environment variables (see below)
3. **Security Keys**: Generate strong API keys and session secrets

## 🔧 Required Environment Variables

### Core Configuration
```bash
# Database (Required)
DATABASE_URL=postgresql://username:password@host:port/database

# Security (Required)
SESSION_SECRET=your-32-character-minimum-session-secret
ADMIN_API_KEY=your-secure-admin-api-key-for-protected-endpoints

# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
```

### Optional Configuration
```bash
# SMTP Email (Optional - email functionality disabled if not provided)
SMTP_SERVER=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
FROM_EMAIL=<EMAIL>
FROM_NAME=SPOT - Assessment Made Easy

# Cloudflare Turnstile (Optional - for bot protection)
TURNSTILE_SECRET_KEY=your-turnstile-secret-key
VITE_TURNSTILE_SITE_KEY=your-turnstile-site-key
```

## 🐳 Coolify/Docker Deployment

### Build Settings
- **Build Command**: `npm run build`
- **Start Command**: `npm start`
- **Port**: 3000 (or whatever PORT env var is set to)
- **Dockerfile**: Uses multi-stage build for security and optimization

### Environment Variables
Set these in Coolify's environment section:
- All required environment variables listed above
- Ensure `ADMIN_API_KEY` is a strong, randomly generated key

### Security Features
- ✅ Non-root user execution
- ✅ Multi-stage Docker build
- ✅ Security headers enabled
- ✅ Rate limiting configured
- ✅ Input validation and sanitization

## ⚡ Vercel Deployment

### Configuration
The `vercel.json` is configured for hybrid deployment:
- Static assets served from CDN
- API routes handled by serverless functions
- Automatic HTTPS and global distribution

### Build Settings
- **Build Command**: `npm run build`
- **Output Directory**: `dist/public`
- **Node.js Version**: 20.x
- **Function Timeout**: 30 seconds

### Environment Variables
Set these in Vercel's environment section:
- All required environment variables listed above
- Vercel automatically provides `NODE_ENV=production`

### Deployment Steps
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on git push to main branch

### Health Check
The application includes a health check endpoint at `/api/health` that:
- Tests database connectivity
- Returns status 200 when healthy
- Returns status 503 when unhealthy

## Database Setup

1. Run database migrations:
```bash
npm run db:push
```

2. The application will create the following tables:
- `users` - User authentication
- `contact_submissions` - Contact form data
- `posts` - Blog posts (CMS)
- `categories` - Blog categories
- `comments` - Blog comments
- `pages` - Static pages
- `sessions` - Session storage

## Troubleshooting

### Common Issues

1. **Build Fails**: Ensure all dependencies are available and NODE_ENV is set correctly
2. **App Won't Start**: Check that DATABASE_URL is properly formatted
3. **Database Connection**: Verify PostgreSQL is accessible and credentials are correct
4. **Port Issues**: Coolify will set PORT automatically, default is 3000

### Debug Commands

Check health status:
```bash
curl https://your-domain.com/api/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-17T...",
  "service": "SPOT Platform"
}
```

## Production Considerations

- The app serves both API and frontend from the same port
- Static files are served from `/dist/public` after build
- Database connections are pooled for performance
- Sessions are stored in PostgreSQL for scalability