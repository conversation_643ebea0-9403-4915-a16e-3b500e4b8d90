# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database
PGHOST=localhost
PGPORT=5432
PGUSER=your_username
PGPASSWORD=your_password
PGDATABASE=your_database

# Email Configuration (<PERSON>ail<PERSON> SMTP)
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_SERVER=your_smtp_server
SMTP_PORT=587
TLS=true
FROM_NAME=SPOT - Assessment Made Easy
FROM_EMAIL=<EMAIL>

# Cloudflare Turnstile (Bot Protection)
TURNSTILE_SECRET_KEY=your_turnstile_secret_key
TURNSTILE_SITE_KEY=your_turnstile_site_key

# Frontend Environment Variables (must be prefixed with VITE_)
VITE_TURNSTILE_SITE_KEY=your_turnstile_site_key

# Application Configuration
NODE_ENV=development
PORT=5000
HOST=0.0.0.0

# Session Security
SESSION_SECRET=your_very_secure_session_secret_here