# Multi-stage build for better security and smaller image size
FROM node:20-alpine AS builder

# Install security updates and build dependencies
RUN apk update && apk upgrade && apk add --no-cache libc6-compat dumb-init

# Set working directory
WORKDIR /app

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci --only=production --ignore-scripts && npm cache clean --force

# Copy source code
COPY . .

# Build the application (frontend and backend)
RUN npm run build

# Production stage
FROM node:20-alpine AS production

# Install security updates and runtime dependencies
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 spotapp

# Set working directory
WORKDIR /app

# Copy built application and dependencies from builder stage
COPY --from=builder --chown=spotapp:nodejs /app/dist ./dist
COPY --from=builder --chown=spotapp:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=spotapp:nodejs /app/package*.json ./

# Switch to non-root user
USER spotapp

# Expose port (Coolify will set PORT env var)
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=1024"

# Health check with improved security
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD node -e "require('http').get('http://localhost:' + (process.env.PORT || 3000) + '/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["npm", "start"]