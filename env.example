# SPOT - Student Proficiency Online Test Platform
# Environment Variables Configuration

# =====================================
# DATABASE CONFIGURATION
# =====================================
# PostgreSQL Database URL (required)
# Format: postgresql://username:password@host:port/database
DATABASE_URL=postgresql://username:password@localhost:5432/spot_database

# =====================================
# SMTP EMAIL CONFIGURATION
# =====================================
# Email service provider settings (optional for deployment)
# If not provided, email functionality will be disabled gracefully

# SMTP Server Configuration
SMTP_SERVER=smtp.emailit.com
SMTP_PORT=587
SMTP_USER=your_email_username
SMTP_PASSWORD=your_email_password
TLS=true

# Email Sender Information
FROM_NAME=SPOT - Assessment Made Easy
FROM_EMAIL=<EMAIL>

# =====================================
# SECURITY & CAPTCHA
# =====================================
# Cloudflare Turnstile (Bot Protection)
# Get these from: https://dash.cloudflare.com/profile/api-tokens
VITE_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# =====================================
# SERVER CONFIGURATION
# =====================================
# Server Runtime Configuration
NODE_ENV=development
HOST=0.0.0.0

# Port Configuration (automatic based on environment)
# Development: 5000, Production: 3000
# PORT=5000  # Optional override

# =====================================
# SESSION MANAGEMENT & SECURITY
# =====================================
# Session Secret for Express Sessions (minimum 32 characters)
SESSION_SECRET=your-super-secret-session-key-change-this-in-production-minimum-32-chars

# Admin API Key for protected endpoints (generate a strong random key)
ADMIN_API_KEY=your-admin-api-key-for-protected-endpoints-change-this

# =====================================
# DEPLOYMENT SPECIFIC
# =====================================
# Replit Environment (automatically set by Replit)
# REPLIT_DOMAINS=your-repl-name.your-username.repl.co
# REPL_ID=your-repl-id

# =====================================
# OPTIONAL INTEGRATIONS
# =====================================
# Analytics & Monitoring (if needed)
# ANALYTICS_ID=your-analytics-id

# CDN Configuration (if using external assets)
# CDN_URL=https://your-cdn-domain.com

# API Rate Limiting (if implementing)
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100

# =====================================
# DEVELOPMENT ONLY
# =====================================
# Debug Mode (development only)
DEBUG=spot:*

# Disable certain security features in development
# DISABLE_SSL_VERIFY=true  # Only for development