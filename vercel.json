{"version": 2, "builds": [{"src": "server/index.ts", "use": "@vercel/node", "config": {"includeFiles": ["dist/public/**"], "maxDuration": 30}}, {"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist/public"}}], "routes": [{"src": "/api/(.*)", "dest": "/server/index.ts"}, {"src": "/(.*)", "dest": "/dist/public/$1"}], "buildCommand": "npm run build", "env": {"NODE_ENV": "production"}}