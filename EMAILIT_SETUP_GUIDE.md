# EmailIt Setup Guide for SPOT Platform

This guide will help you set up EmailIt for sending emails from your SPOT platform contact form.

## What is EmailIt?

EmailIt is a reliable email API service that provides better deliverability and easier setup compared to traditional SMTP. The platform has been migrated from Nodemailer/SMTP to EmailIt API.

## Setup Steps

### 1. Create EmailIt Account

1. Go to [EmailIt.com](https://emailit.com)
2. Sign up for an account
3. Verify your email address
4. Log in to the EmailIt dashboard

### 2. Create a Sending Domain

1. In the EmailIt dashboard, go to **Sending Domains**
2. Click **Add Domain**
3. Enter your domain (e.g., `mispot.in`)
4. Follow the DNS verification steps:
   - Add the required DNS records to your domain
   - Wait for verification (usually takes a few minutes)

### 3. Create API Credentials

1. Go to **Credentials** in the dashboard
2. Click **Create Credential**
3. Select **API** as the credential type
4. Give it a name (e.g., "SPOT Platform")
5. Copy the generated API key - you'll need this for the environment variable

### 4. Set Environment Variables

Add the following environment variables to your deployment:

```bash
# Required: EmailIt API Key
EMAILIT_API_KEY=your_emailit_api_key_here

# Optional: Email sender details (defaults provided)
FROM_EMAIL=<EMAIL>
FROM_NAME=SPOT - Assessment Made Easy
```

### 5. Verify Setup

After setting the environment variables:

1. Restart your application
2. Check the system status at `/api/system-status`
3. Look for the email service status - it should show "healthy"
4. Test the contact form to ensure emails are being sent

## Environment Variables Reference

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `EMAILIT_API_KEY` | ✅ Yes | - | Your EmailIt API key from the dashboard |
| `FROM_EMAIL` | ❌ No | `<EMAIL>` | Email address that appears as sender |
| `FROM_NAME` | ❌ No | `SPOT - Assessment Made Easy` | Name that appears as sender |

## Troubleshooting

### Common Issues

#### 1. "EmailIt not configured" Error
- **Cause**: `EMAILIT_API_KEY` environment variable is not set
- **Solution**: Add the API key to your environment variables and restart the application

#### 2. "Invalid API key" Error
- **Cause**: The API key is incorrect or expired
- **Solution**: 
  - Check the API key in EmailIt dashboard
  - Generate a new API key if needed
  - Update the environment variable

#### 3. "Domain not verified" Error
- **Cause**: Your sending domain is not verified in EmailIt
- **Solution**: 
  - Complete the domain verification process in EmailIt dashboard
  - Ensure all DNS records are properly configured

#### 4. Emails not being delivered
- **Cause**: Various reasons including domain reputation, content filters
- **Solution**: 
  - Check EmailIt dashboard for delivery logs
  - Ensure your domain has proper SPF, DKIM, and DMARC records
  - Contact EmailIt support if needed

### Debug Information

The application provides detailed logging for email operations:

1. **Server Logs**: Check server console for detailed email sending logs
2. **System Status**: Visit `/api/system-status` to check email service health
3. **Debug Panel**: Enable debug mode in browser console with `debugUtils.enable()`

### Log Examples

**Successful Email Send:**
```
[abc123] Starting acknowledgement email send { to: '<EMAIL>', method: 'EmailIt API' }
[abc123] Sending acknowledgement email via EmailIt API
[abc123] Acknowledgement email sent successfully { emailId: 'email_xyz', status: 'sent' }
```

**Configuration Error:**
```
[abc123] EmailIt not configured - skipping acknowledgement email
[abc123] Set EMAILIT_API_KEY environment variable to enable email sending
```

## Migration from SMTP

If you were previously using SMTP configuration, the following changes have been made:

### Removed Environment Variables
- `SMTP_SERVER`
- `SMTP_PORT` 
- `SMTP_USER`
- `SMTP_PASSWORD`
- `TLS`

### New Environment Variables
- `EMAILIT_API_KEY` (required)

### Benefits of EmailIt over SMTP
- ✅ Better deliverability rates
- ✅ No need to manage SMTP server credentials
- ✅ Built-in analytics and delivery tracking
- ✅ Automatic retry and error handling
- ✅ Better security (API key vs password)

## Support

### EmailIt Support
- Documentation: [docs.emailit.com](https://docs.emailit.com)
- Discord: [discord.emailit.com](https://discord.emailit.com)
- Email: Contact through their dashboard

### Application Support
- Check server logs for detailed error information
- Use the debug panel for client-side debugging
- Review the system status endpoint for health checks

## Testing

To test your EmailIt setup:

1. **System Status Check**:
   ```bash
   curl https://your-domain.com/api/system-status
   ```

2. **Contact Form Test**:
   - Fill out the contact form on your website
   - Check server logs for email sending confirmation
   - Verify emails are received

3. **Debug Mode**:
   ```javascript
   // In browser console
   debugUtils.enable()
   // Then test the contact form and check debug panel
   ```

## Security Notes

- Keep your EmailIt API key secure and never commit it to version control
- Use environment variables for all sensitive configuration
- Regularly rotate your API keys for security
- Monitor your EmailIt dashboard for unusual activity

## Cost Considerations

- EmailIt typically charges per email sent
- Monitor your usage in the EmailIt dashboard
- Set up billing alerts if available
- Consider your expected email volume when choosing a plan
