@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 98%);
  --foreground: hsl(210, 12%, 13%);
  --muted: hsl(210, 10%, 96%);
  --muted-foreground: hsl(215, 8%, 46%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(210, 12%, 13%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(210, 12%, 13%);
  --border: hsl(214, 13%, 90%);
  --input: hsl(214, 13%, 90%);
  --primary: hsl(207, 77%, 54%);
  --primary-foreground: hsl(210, 100%, 99%);
  --secondary: hsl(210, 10%, 96%);
  --secondary-foreground: hsl(210, 12%, 13%);
  --accent: hsl(30, 100%, 49%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(60, 9%, 98%);
  --ring: hsl(207, 77%, 54%);
  --radius: 0.5rem;
  
  /* Custom SPOT colors */
  --spot-primary: hsl(207, 77%, 54%);
  --spot-secondary: hsl(194, 100%, 37%);
  --spot-accent: hsl(30, 100%, 49%);
  --spot-success: hsl(122, 39%, 49%);
  --spot-text-primary: hsl(210, 12%, 13%);
  --spot-text-secondary: hsl(215, 8%, 46%);
  --spot-surface: hsl(0, 0%, 100%);
  --spot-background: hsl(0, 0%, 98%);
}

.dark {
  --background: hsl(240, 10%, 4%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 4%, 16%);
  --muted-foreground: hsl(240, 5%, 65%);
  --popover: hsl(240, 10%, 4%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 4%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 4%, 16%);
  --input: hsl(240, 4%, 16%);
  --primary: hsl(207, 77%, 54%);
  --primary-foreground: hsl(210, 100%, 99%);
  --secondary: hsl(240, 4%, 16%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(30, 100%, 49%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 63%, 31%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(207, 77%, 54%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
  }
}

@layer utilities {
  .spot-primary {
    color: var(--spot-primary);
  }
  
  .spot-secondary {
    color: var(--spot-secondary);
  }
  
  .spot-accent {
    color: var(--spot-accent);
  }
  
  .bg-spot-primary {
    background-color: var(--spot-primary);
  }
  
  .bg-spot-secondary {
    background-color: var(--spot-secondary);
  }
  
  .bg-spot-accent {
    background-color: var(--spot-accent);
  }
  
  .bg-spot-surface {
    background-color: var(--spot-surface);
  }
  
  .bg-spot-background {
    background-color: var(--spot-background);
  }
  
  .text-spot-primary {
    color: var(--spot-text-primary);
  }
  
  .text-spot-secondary {
    color: var(--spot-text-secondary);
  }
}
