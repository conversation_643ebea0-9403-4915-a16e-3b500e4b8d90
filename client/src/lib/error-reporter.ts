interface ErrorReport {
  timestamp: string;
  url: string;
  userAgent: string;
  error: {
    message: string;
    stack?: string;
    name: string;
  };
  context?: Record<string, any>;
  sessionId: string;
}

class ErrorReporter {
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
  }

  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.reportError(event.error || new Error(event.message), {
        type: 'javascript_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError(new Error(event.reason), {
        type: 'unhandled_promise_rejection',
        reason: event.reason
      });
    });
  }

  reportError(error: Error, context?: Record<string, any>): void {
    const report: ErrorReport = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context: {
        ...context,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        screen: {
          width: screen.width,
          height: screen.height
        },
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
          rtt: (navigator as any).connection.rtt
        } : null
      },
      sessionId: this.sessionId
    };

    // Log to console for immediate debugging
    console.error('Error Report:', report);

    // Store in localStorage for debugging (keep last 10 errors)
    this.storeErrorLocally(report);

    // In a production environment, you might want to send this to a logging service
    // this.sendToLoggingService(report);
  }

  private storeErrorLocally(report: ErrorReport): void {
    try {
      const key = 'mispot_error_reports';
      const stored = localStorage.getItem(key);
      const reports = stored ? JSON.parse(stored) : [];
      
      reports.push(report);
      
      // Keep only the last 10 reports
      if (reports.length > 10) {
        reports.splice(0, reports.length - 10);
      }
      
      localStorage.setItem(key, JSON.stringify(reports));
    } catch (e) {
      console.warn('Failed to store error report locally:', e);
    }
  }

  getStoredErrors(): ErrorReport[] {
    try {
      const stored = localStorage.getItem('mispot_error_reports');
      return stored ? JSON.parse(stored) : [];
    } catch (e) {
      console.warn('Failed to retrieve stored error reports:', e);
      return [];
    }
  }

  clearStoredErrors(): void {
    try {
      localStorage.removeItem('mispot_error_reports');
    } catch (e) {
      console.warn('Failed to clear stored error reports:', e);
    }
  }

  // Method to manually report contact form specific errors
  reportContactFormError(error: Error, formData?: Record<string, any>): void {
    this.reportError(error, {
      type: 'contact_form_error',
      formData: formData ? this.sanitizeFormData(formData) : null,
      turnstileLoaded: typeof window.turnstile !== 'undefined',
      turnstileReady: window.turnstile?.ready || false
    });
  }

  private sanitizeFormData(formData: Record<string, any>): Record<string, any> {
    const sanitized = { ...formData };
    
    // Remove sensitive information
    delete sanitized.turnstileToken;
    
    // Keep only structure information for debugging
    return {
      hasEmail: !!sanitized.email,
      hasFirstName: !!sanitized.firstName,
      hasLastName: !!sanitized.lastName,
      hasPhone: !!sanitized.phone,
      hasInstitution: !!sanitized.institution,
      hasStudentCount: !!sanitized.studentCount,
      hasMessage: !!sanitized.message,
      emailLength: sanitized.email?.length || 0,
      institutionLength: sanitized.institution?.length || 0
    };
  }

  // Method to get system information for debugging
  getSystemInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      languages: navigator.languages,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      url: window.location.href,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId
    };
  }
}

// Create a singleton instance
export const errorReporter = new ErrorReporter();

// Export types for use in other files
export type { ErrorReport };
