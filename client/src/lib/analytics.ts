// Google Analytics utility functions
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Track page views
export const trackPageView = (url: string, title?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-80LVVMD5F4', {
      page_location: url,
      page_title: title,
    });
  }
};

// Track custom events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  try {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value,
      });
    }
  } catch (error) {
    console.log('Analytics tracking failed:', error);
  }
};

// Safe wrapper for analytics functions
const safeTrack = (fn: () => void) => {
  try {
    fn();
  } catch (error) {
    console.log('Analytics tracking failed:', error);
  }
};

// Predefined event tracking functions for common actions
export const analytics = {
  // Contact form events
  contactForm: {
    started: () => safeTrack(() => trackEvent('form_start', 'contact', 'demo_request')),
    completed: () => safeTrack(() => trackEvent('form_submit', 'contact', 'demo_request')),
    failed: (error: string) => safeTrack(() => trackEvent('form_error', 'contact', error)),
  },

  // Navigation events
  navigation: {
    menuClick: (item: string) => safeTrack(() => trackEvent('click', 'navigation', item)),
    ctaClick: (cta: string) => safeTrack(() => trackEvent('click', 'cta', cta)),
    socialClick: (platform: string) => safeTrack(() => trackEvent('click', 'social', platform)),
  },

  // Engagement events
  engagement: {
    scrollToSection: (section: string) => safeTrack(() => trackEvent('scroll', 'engagement', section)),
    videoPlay: (video: string) => safeTrack(() => trackEvent('play', 'video', video)),
    downloadBrochure: () => safeTrack(() => trackEvent('download', 'resource', 'brochure')),
  },

  // Business events
  business: {
    demoRequest: () => safeTrack(() => trackEvent('demo_request', 'business', 'spot_platform')),
    pricingView: () => safeTrack(() => trackEvent('view', 'pricing', 'spot_platform')),
    featureExplore: (feature: string) => safeTrack(() => trackEvent('explore', 'feature', feature)),
  },

  // Error tracking
  errors: {
    jsError: (error: string) => safeTrack(() => trackEvent('error', 'javascript', error)),
    apiError: (endpoint: string) => safeTrack(() => trackEvent('error', 'api', endpoint)),
    formValidation: (field: string) => safeTrack(() => trackEvent('error', 'validation', field)),
  },
};

// Enhanced event tracking with user properties
export const trackUserAction = (
  action: string,
  properties: Record<string, any> = {}
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      custom_map: properties,
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
      page_location: window.location.href,
      page_title: document.title,
    });
  }
};

// Track conversion events
export const trackConversion = (
  conversionId: string,
  value?: number,
  currency: string = 'INR'
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'conversion', {
      send_to: conversionId,
      value: value,
      currency: currency,
    });
  }
};

// Set user properties
export const setUserProperties = (properties: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-80LVVMD5F4', {
      custom_map: properties,
    });
  }
};

// Track timing events (for performance monitoring)
export const trackTiming = (
  name: string,
  value: number,
  category: string = 'performance'
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'timing_complete', {
      name: name,
      value: value,
      event_category: category,
    });
  }
};

// Debug mode for development
export const enableDebugMode = () => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-80LVVMD5F4', {
      debug_mode: true,
    });
  }
};

// Disable analytics (for privacy compliance)
export const disableAnalytics = () => {
  if (typeof window !== 'undefined') {
    window[`ga-disable-G-80LVVMD5F4`] = true;
  }
};

// Enable analytics
export const enableAnalytics = () => {
  if (typeof window !== 'undefined') {
    window[`ga-disable-G-80LVVMD5F4`] = false;
  }
};

// Check if analytics is enabled
export const isAnalyticsEnabled = (): boolean => {
  if (typeof window !== 'undefined') {
    return !window[`ga-disable-G-80LVVMD5F4`];
  }
  return false;
};

// Initialize analytics with consent
export const initializeAnalytics = (hasConsent: boolean = true) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('consent', 'default', {
      analytics_storage: hasConsent ? 'granted' : 'denied',
      ad_storage: hasConsent ? 'granted' : 'denied',
    });

    if (hasConsent) {
      window.gtag('config', 'G-80LVVMD5F4', {
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false,
      });
    }
  }
};

// Update consent
export const updateConsent = (hasConsent: boolean) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('consent', 'update', {
      analytics_storage: hasConsent ? 'granted' : 'denied',
      ad_storage: hasConsent ? 'granted' : 'denied',
    });
  }
};
