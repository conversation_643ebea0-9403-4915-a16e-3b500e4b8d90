import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    // Clone the response to read it multiple times
    const clonedRes = res.clone();
    let errorText = res.statusText;

    try {
      errorText = await clonedRes.text();
    } catch (e) {
      console.warn('Failed to read error response text:', e);
    }

    // Create a detailed error that includes the response
    const error = new Error(`${res.status}: ${errorText}`);
    (error as any).response = res;
    (error as any).status = res.status;
    (error as any).statusText = res.statusText;

    console.error('API request failed:', {
      status: res.status,
      statusText: res.statusText,
      url: res.url,
      type: res.type,
      headers: Object.fromEntries(res.headers.entries()),
      errorText: errorText
    });

    throw error;
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2);

  console.log(`[${requestId}] Making API request`, {
    method,
    url,
    hasData: !!data,
    dataKeys: data && typeof data === 'object' ? Object.keys(data) : [],
    timestamp: new Date().toISOString()
  });

  try {
    const res = await fetch(url, {
      method,
      headers: data ? { "Content-Type": "application/json" } : {},
      body: data ? JSON.stringify(data) : undefined,
      credentials: "include",
    });

    console.log(`[${requestId}] Received response`, {
      status: res.status,
      statusText: res.statusText,
      ok: res.ok,
      type: res.type,
      url: res.url,
      headers: Object.fromEntries(res.headers.entries())
    });

    await throwIfResNotOk(res);
    return res;
  } catch (error) {
    console.error(`[${requestId}] API request failed`, {
      error: error.message,
      stack: error.stack,
      name: error.name,
      method,
      url,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
