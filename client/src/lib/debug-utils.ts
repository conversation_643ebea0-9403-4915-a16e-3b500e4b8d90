// Debug utilities for troubleshooting

export function enableDebugMode(): void {
  localStorage.setItem('debug_mode', 'true');
  console.log('Debug mode enabled. Refresh the page to see the debug panel.');
}

export function disableDebugMode(): void {
  localStorage.removeItem('debug_mode');
  console.log('Debug mode disabled. Refresh the page to hide the debug panel.');
}

export function isDebugModeEnabled(): boolean {
  return localStorage.getItem('debug_mode') === 'true' || process.env.NODE_ENV === 'development';
}

// Add debug utilities to window object for easy access in console
if (typeof window !== 'undefined') {
  (window as any).debugUtils = {
    enable: enableDebugMode,
    disable: disableDebugMode,
    isEnabled: isDebugModeEnabled,
    clearErrors: () => {
      localStorage.removeItem('mispot_error_reports');
      console.log('Error reports cleared.');
    },
    getErrors: () => {
      const stored = localStorage.getItem('mispot_error_reports');
      return stored ? JSON.parse(stored) : [];
    },
    testError: () => {
      throw new Error('Test error for debugging purposes');
    },
    systemInfo: () => {
      return {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        screen: {
          width: screen.width,
          height: screen.height
        },
        url: window.location.href,
        timestamp: new Date().toISOString()
      };
    }
  };

  console.log('Debug utilities available at window.debugUtils');
  console.log('Commands:');
  console.log('  debugUtils.enable() - Enable debug mode');
  console.log('  debugUtils.disable() - Disable debug mode');
  console.log('  debugUtils.clearErrors() - Clear stored error reports');
  console.log('  debugUtils.getErrors() - Get stored error reports');
  console.log('  debugUtils.testError() - Throw a test error');
  console.log('  debugUtils.systemInfo() - Get system information');
}
