import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Shield } from "lucide-react";
import { loginSchema, type LoginData } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { toast } from "@/hooks/use-toast";

export default function Login() {
  const [, setLocation] = useLocation();
  const [isSetupMode, setIsSetupMode] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<LoginData>({
    resolver: zodResolver(loginSchema),
  });

  const {
    register: registerSetup,
    handleSubmit: handleSetupSubmit,
    formState: { errors: setupErrors },
    reset: resetSetup
  } = useForm({
    defaultValues: {
      username: "",
      password: "",
      email: "",
      firstName: "",
      lastName: ""
    }
  });

  // Check if setup is needed
  const { data: setupStatus, isLoading: setupLoading } = useQuery({
    queryKey: ["setup-status"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/auth/setup-needed");
      return response.json();
    },
  });

  // Check current authentication status
  const { data: authStatus } = useQuery({
    queryKey: ["auth-status"],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/auth/me");
      return response.json();
    },
    retry: false,
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (authStatus?.success) {
      setLocation("/admin");
    }
  }, [authStatus, setLocation]);

  // Set setup mode based on setup status
  useEffect(() => {
    if (setupStatus?.needsSetup) {
      setIsSetupMode(true);
    }
  }, [setupStatus]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (data: LoginData) => {
      const response = await apiRequest("POST", "/api/auth/login", data);
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Login successful",
        });
        setLocation("/admin");
      } else {
        toast({
          title: "Error",
          description: data.message || "Login failed",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Login failed. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Setup mutation
  const setupMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/auth/setup", data);
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Success",
          description: "Admin user created successfully. You can now login.",
        });
        setIsSetupMode(false);
        resetSetup();
      } else {
        toast({
          title: "Error",
          description: data.message || "Setup failed",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Setup failed. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onLogin = (data: LoginData) => {
    loginMutation.mutate(data);
  };

  const onSetup = (data: any) => {
    setupMutation.mutate(data);
  };

  if (setupLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
            <Shield className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {isSetupMode ? "Initial Setup" : "Admin Login"}
          </CardTitle>
          <CardDescription>
            {isSetupMode 
              ? "Create the first admin user to get started"
              : "Access the SPOT admin dashboard"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isSetupMode ? (
            <form onSubmit={handleSetupSubmit(onSetup)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    {...registerSetup("firstName", { required: "First name is required" })}
                    disabled={setupMutation.isPending}
                  />
                  {setupErrors.firstName && (
                    <p className="text-sm text-red-600">{setupErrors.firstName.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    {...registerSetup("lastName", { required: "Last name is required" })}
                    disabled={setupMutation.isPending}
                  />
                  {setupErrors.lastName && (
                    <p className="text-sm text-red-600">{setupErrors.lastName.message}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  {...registerSetup("email", { required: "Email is required" })}
                  disabled={setupMutation.isPending}
                />
                {setupErrors.email && (
                  <p className="text-sm text-red-600">{setupErrors.email.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  {...registerSetup("username", { required: "Username is required" })}
                  disabled={setupMutation.isPending}
                />
                {setupErrors.username && (
                  <p className="text-sm text-red-600">{setupErrors.username.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  {...registerSetup("password", { required: "Password is required" })}
                  disabled={setupMutation.isPending}
                />
                {setupErrors.password && (
                  <p className="text-sm text-red-600">{setupErrors.password.message}</p>
                )}
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={setupMutation.isPending}
              >
                {setupMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Create Admin User
              </Button>
            </form>
          ) : (
            <form onSubmit={handleSubmit(onLogin)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  {...register("username")}
                  disabled={loginMutation.isPending}
                />
                {errors.username && (
                  <p className="text-sm text-red-600">{errors.username.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  {...register("password")}
                  disabled={loginMutation.isPending}
                />
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={loginMutation.isPending}
              >
                {loginMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Sign In
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
