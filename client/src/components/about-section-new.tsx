import { Trophy, Users, Lightbulb, GraduationCap, Building2, Award } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";

export default function AboutSection() {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="about" className="py-20 bg-gradient-to-r from-spot-background to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="font-bold text-4xl text-spot-primary mb-6">
              Powered by 25+ Years of Educational Excellence
            </h2>
            <p className="text-xl text-spot-secondary mb-8">
              MinervaInfo has been at the forefront of educational technology innovation, empowering institutions across India with proven solutions that drive student success.
            </p>
            
            <div className="space-y-6">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-spot-primary text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Trophy className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-spot-primary mb-2">Industry Leadership</h3>
                  <p className="text-spot-secondary">Over two decades of experience in providing cutting-edge EdTech solutions to educational institutions.</p>
                </div>
              </motion.div>
              
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-spot-accent text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Users className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-spot-primary mb-2">Proven Impact</h3>
                  <p className="text-spot-secondary">Empowering 500,000+ active students across 900+ partner schools and colleges nationwide.</p>
                </div>
              </motion.div>
              
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-green-500 text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Lightbulb className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-spot-primary mb-2">Innovation Commitment</h3>
                  <p className="text-spot-secondary">Continuously evolving our technology to meet the changing needs of modern education.</p>
                </div>
              </motion.div>
            </div>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-8 space-x-4"
            >
              <Button 
                onClick={scrollToContact}
                className="bg-spot-primary hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
              >
                Learn About MinervaInfo
              </Button>
              <Button 
                variant="outline"
                className="border border-spot-primary text-spot-primary hover:bg-spot-primary hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
              >
                Download Brochure
              </Button>
            </motion.div>
          </motion.div>
          
          <StatisticsInfographic />
        </div>
      </div>
    </section>
  );
}

const StatisticsInfographic = () => {
  const [studentsCount, setStudentsCount] = useState(0);
  const [schoolsCount, setSchoolsCount] = useState(0);
  const [yearsCount, setYearsCount] = useState(0);

  const statistics = [
    {
      icon: GraduationCap,
      count: 500000,
      suffix: "+",
      label: "Empowered Active Students",
      description: "Students benefiting from our platform",
      color: "from-blue-500 to-indigo-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      icon: Building2,
      count: 900,
      suffix: "+",
      label: "Schools & Colleges Educational Partners",
      description: "Trusted educational institutions",
      color: "from-purple-500 to-pink-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      icon: Award,
      count: 25,
      suffix: "+",
      label: "EdTech Excellence Years of Experience",
      description: "Years of proven track record",
      color: "from-emerald-500 to-teal-600",
      bgColor: "bg-emerald-50",
      iconColor: "text-emerald-600"
    }
  ];

  // Trigger animations when component comes into view
  useEffect(() => {
    const animateCount = (target: number, setter: (value: number) => void, duration = 2000) => {
      let startTime: number;
      let animationFrame: number;

      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(easeOutQuart * target);
        
        setter(currentValue);
        
        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);
      return () => cancelAnimationFrame(animationFrame);
    };

    // Start animations with staggered delays
    const timer1 = setTimeout(() => animateCount(500000, setStudentsCount), 500);
    const timer2 = setTimeout(() => animateCount(900, setSchoolsCount), 800);
    const timer3 = setTimeout(() => animateCount(25, setYearsCount), 1100);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);

  return (
    <motion.div 
      initial={{ opacity: 0, x: 30 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="relative"
    >
      <div className="space-y-4">
        {statistics.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, delay: index * 0.15 }}
            viewport={{ once: true }}
            className="relative overflow-hidden rounded-xl bg-white shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5`} />
            
            <div className="relative p-6">
              <div className="flex items-center space-x-4">
                <div className={`inline-flex items-center justify-center w-12 h-12 ${stat.bgColor} rounded-xl flex-shrink-0`}>
                  <stat.icon className={`w-6 h-6 ${stat.iconColor}`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <motion.div 
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.4, delay: index * 0.15 + 0.2 }}
                    viewport={{ once: true }}
                    className="text-2xl lg:text-3xl font-bold text-gray-900 mb-1"
                  >
                    {index === 0 && `${studentsCount.toLocaleString()}${stat.suffix}`}
                    {index === 1 && `${schoolsCount.toLocaleString()}${stat.suffix}`}
                    {index === 2 && `${yearsCount}${stat.suffix}`}
                  </motion.div>
                  
                  <h3 className="text-sm font-semibold text-gray-900 mb-1 leading-tight">
                    {stat.label}
                  </h3>
                  
                  <p className="text-gray-600 text-xs">
                    {stat.description}
                  </p>
                </div>
                
                <div className="hidden lg:block flex-shrink-0">
                  <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-full opacity-10`} />
                </div>
              </div>
            </div>
            
            <div className="absolute top-2 right-2 w-8 h-8 bg-gradient-to-br from-white/20 to-transparent rounded-full blur-sm" />
          </motion.div>
        ))}
      </div>
      
      <motion.div 
        animate={{ y: [-3, 3, -3] }}
        transition={{ duration: 3, repeat: Infinity }}
        className="absolute -top-3 -right-3 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg z-10"
      >
        <Trophy className="w-3 h-3 inline mr-1" />
        Leader
      </motion.div>
      
      <div className="absolute left-6 top-16 bottom-16 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-emerald-200 opacity-20" />
    </motion.div>
  );
};