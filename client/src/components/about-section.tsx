import { Trophy, Users, Lightbulb, GraduationCap, Building2, Award } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";

const StatisticsInfographic = () => {
  const [studentsCount, setStudentsCount] = useState(0);
  const [schoolsCount, setSchoolsCount] = useState(0);
  const [yearsCount, setYearsCount] = useState(0);

  const statistics = [
    {
      icon: GraduationCap,
      count: 500000,
      suffix: "+",
      label: "Empowered Active Students",
      description: "Students benefiting from our platform",
      color: "from-blue-500 to-indigo-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      icon: Building2,
      count: 900,
      suffix: "+",
      label: "Schools & Colleges Educational Partners",
      description: "Trusted educational institutions",
      color: "from-purple-500 to-pink-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      icon: Award,
      count: 25,
      suffix: "+",
      label: "EdTech Excellence Years of Experience",
      description: "Years of proven track record",
      color: "from-emerald-500 to-teal-600",
      bgColor: "bg-emerald-50",
      iconColor: "text-emerald-600"
    }
  ];

  const AnimatedCounter = ({ targetValue, setValue, duration = 2000 }: { targetValue: number, setValue: (value: number) => void, duration?: number }) => {
    useEffect(() => {
      let startTime: number;
      let animationFrame: number;

      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(easeOutQuart * targetValue);
        
        setValue(currentValue);
        
        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);
      return () => cancelAnimationFrame(animationFrame);
    }, [targetValue, setValue, duration]);

    return null;
  };

  return (
    <motion.div 
      initial={{ opacity: 0, x: 30 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="relative"
    >
      {/* Main Statistics Grid */}
      <div className="space-y-6">
        {statistics.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            viewport={{ once: true }}
            className="relative overflow-hidden rounded-2xl bg-white shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300"
          >
            {/* Background Gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5`} />
            
            <div className="relative p-8">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center w-16 h-16 ${stat.bgColor} rounded-2xl mb-4`}>
                    <stat.icon className={`w-8 h-8 ${stat.iconColor}`} />
                  </div>
                  
                  {/* Counter */}
                  <div className="mb-3">
                    <motion.div 
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
                      viewport={{ once: true }}
                      className="text-4xl lg:text-5xl font-bold text-gray-900"
                    >
                      {index === 0 && (
                        <>
                          {studentsCount.toLocaleString()}{stat.suffix}
                          <AnimatedCounter targetValue={stat.count} setValue={setStudentsCount} />
                        </>
                      )}
                      {index === 1 && (
                        <>
                          {schoolsCount.toLocaleString()}{stat.suffix}
                          <AnimatedCounter targetValue={stat.count} setValue={setSchoolsCount} />
                        </>
                      )}
                      {index === 2 && (
                        <>
                          {yearsCount}{stat.suffix}
                          <AnimatedCounter targetValue={stat.count} setValue={setYearsCount} />
                        </>
                      )}
                    </motion.div>
                  </div>
                  
                  {/* Label */}
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                    {stat.label}
                  </h3>
                  
                  {/* Description */}
                  <p className="text-gray-600 text-sm">
                    {stat.description}
                  </p>
                </div>
                
                {/* Visual Elements */}
                <div className="hidden lg:block">
                  <div className={`w-24 h-24 bg-gradient-to-br ${stat.color} rounded-full opacity-10`} />
                </div>
              </div>
            </div>
            
            {/* Decorative Elements */}
            <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-white/20 to-transparent rounded-full blur-xl" />
            <div className="absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-tr from-white/10 to-transparent rounded-full blur-lg" />
          </motion.div>
        ))}
      </div>
      
      {/* Floating Achievement Badge */}
      <motion.div 
        animate={{ y: [-5, 5, -5] }}
        transition={{ duration: 4, repeat: Infinity }}
        className="absolute -top-4 -right-4 bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg z-10"
      >
        <Trophy className="w-4 h-4 inline mr-1" />
        Industry Leader
      </motion.div>
      
      {/* Connection Lines (Decorative) */}
      <div className="absolute left-8 top-32 bottom-32 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-emerald-200 opacity-30" />
    </motion.div>
  );
};

export default function AboutSection() {
  const scrollToContact = () => {
    try {
      const element = document.getElementById('contact');
      if (element) {
        const navHeight = 64;
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - navHeight;
        
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    } catch (error) {
      console.error('Error scrolling to contact:', error);
    }
  };

  return (
    <section id="about" className="py-20 bg-gradient-to-r from-spot-background to-gray-50 scroll-mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="font-bold text-4xl text-spot-primary mb-6">
              Powered by 25+ Years of Educational Excellence
            </h2>
            <p className="text-xl text-spot-secondary mb-8">
              MinervaInfo has been at the forefront of educational technology innovation, empowering institutions across India with proven solutions that drive student success.
            </p>
            
            <div className="space-y-6">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-spot-primary text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Trophy className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-spot-primary mb-2">Industry Leadership</h3>
                  <p className="text-spot-secondary">Over two decades of experience in providing cutting-edge EdTech solutions to educational institutions.</p>
                </div>
              </motion.div>
              
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-spot-accent text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Users className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-spot-primary mb-2">Proven Impact</h3>
                  <p className="text-spot-secondary">Empowering 500,000+ active students across 900+ partner schools and colleges nationwide.</p>
                </div>
              </motion.div>
              
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-green-500 text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Lightbulb className="h-4 w-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-spot-primary mb-2">Innovation Commitment</h3>
                  <p className="text-spot-secondary">Continuously evolving our technology to meet the changing needs of modern education.</p>
                </div>
              </motion.div>
            </div>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-8 space-x-4"
            >
              <Button 
                onClick={scrollToContact}
                className="bg-spot-primary hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
              >
                Learn About MinervaInfo
              </Button>
              <Button 
                variant="outline"
                className="border border-spot-primary text-spot-primary hover:bg-spot-primary hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
              >
                Download Brochure
              </Button>
            </motion.div>
          </motion.div>
          
          {/* Statistics Infographic Component */}
          <motion.div 
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative"
          >
            <StatisticsInfographic />
          </motion.div>
        </div>
      </div>
    </section>
  );
}


