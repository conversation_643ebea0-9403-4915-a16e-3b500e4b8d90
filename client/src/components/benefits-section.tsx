import { School, GraduationCap, Check, Star } from "lucide-react";
import { motion } from "framer-motion";

export default function BenefitsSection() {
  const instituteBenefits = [
    {
      title: "Streamlined Operations",
      description: "Reduce manual effort in exam creation, administration, and evaluation processes."
    },
    {
      title: "Data-Driven Decisions",
      description: "Make informed decisions with comprehensive analytics and performance insights."
    },
    {
      title: "Enhanced Reputation",
      description: "Build trust with modern assessment tools that demonstrate educational excellence."
    },
    {
      title: "Improved Outcomes",
      description: "Track and improve student performance with personalized learning insights."
    }
  ];

  const studentBenefits = [
    {
      title: "Personalized Learning",
      description: "Receive customized learning paths based on individual strengths and weaknesses."
    },
    {
      title: "Clear Progress Tracking",
      description: "Understand performance trends and identify areas for improvement with detailed analytics."
    },
    {
      title: "Enhanced Test Skills",
      description: "Improve test-taking strategies and time management through practice and feedback."
    },
    {
      title: "Competitive Exam Prep",
      description: "Better preparation for JEE, NEET, UPSC, and other competitive examinations."
    }
  ];

  return (
    <section id="benefits" className="py-20 bg-spot-background scroll-mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-bold text-4xl text-spot-primary mb-4">
            Transform Your Educational Impact
          </h2>
          <p className="text-xl text-spot-secondary max-w-3xl mx-auto">
            Discover how SPOT drives success for both educational institutions and their students across India.
          </p>
        </motion.div>
        
        <div className="grid lg:grid-cols-2 gap-16">
          {/* Benefits for Institutes */}
          <motion.div 
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="text-center lg:text-left">
              <h3 className="font-semibold text-2xl text-spot-primary mb-4">
                <School className="inline-block text-spot-primary mr-3 h-8 w-8" />
                For Educational Institutes
              </h3>
              <p className="text-spot-secondary">
                Streamline operations and enhance institutional reputation with data-driven insights.
              </p>
            </div>
            
            {instituteBenefits.map((benefit, index) => (
              <motion.div 
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-spot-primary text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Check className="h-4 w-4" />
                </div>
                <div>
                  <h4 className="font-semibold text-spot-primary mb-2">{benefit.title}</h4>
                  <p className="text-spot-secondary">{benefit.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
          
          {/* Benefits for Students */}
          <motion.div 
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="text-center lg:text-left">
              <h3 className="font-semibold text-2xl text-spot-primary mb-4">
                <GraduationCap className="inline-block text-spot-accent mr-3 h-8 w-8" />
                For Students
              </h3>
              <p className="text-spot-secondary">
                Empower students with personalized learning paths and comprehensive preparation tools.
              </p>
            </div>
            
            {studentBenefits.map((benefit, index) => (
              <motion.div 
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="bg-spot-accent text-white p-2 rounded-lg flex-shrink-0 mt-1">
                  <Star className="h-4 w-4" />
                </div>
                <div>
                  <h4 className="font-semibold text-spot-primary mb-2">{benefit.title}</h4>
                  <p className="text-spot-secondary">{benefit.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
}
