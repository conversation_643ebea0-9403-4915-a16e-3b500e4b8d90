import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";

interface ScrollEffectWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export default function ScrollEffectWrapper({ children, className = "" }: ScrollEffectWrapperProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  // Transform scroll progress to opacity and scale values
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.7, 1, 1, 0.7]);
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.95, 1, 1, 0.95]);
  const y = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [20, 0, 0, -20]);

  return (
    <motion.div
      ref={ref}
      style={{ opacity, scale, y }}
      className={`transition-all duration-300 ${className}`}
    >
      {children}
    </motion.div>
  );
}