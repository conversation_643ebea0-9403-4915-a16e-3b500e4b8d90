import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Play, CheckCircle, Star } from "lucide-react";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { analytics } from "@/lib/analytics";

// Product screenshots for hero carousel
const productImages = [
  { src: "/images/SPOT_TEASER_SS/Dashboard.jpg", title: "Admin Dashboard", description: "Comprehensive exam management" },
  { src: "/images/SPOT_TEASER_SS/Login_form.jpg", title: "Secure Login", description: "Multi-level authentication" },
  { src: "/images/SPOT_TEASER_SS/student_dashboard.jpg", title: "Student Portal", description: "Intuitive student interface" },
  { src: "/images/SPOT_TEASER_SS/exam_start_window.jpg", title: "Exam Interface", description: "Seamless test experience" },
  { src: "/images/SPOT_TEASER_SS/question_panel.jpg", title: "Question Management", description: "Advanced question builder" },
  { src: "/images/SPOT_TEASER_SS/candidate_performance_report.jpg", title: "Analytics & Reports", description: "Detailed performance insights" }
];

const ScrollingImages = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % productImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative">
      {/* Main Image Container - Adjusted height for better balance */}
      <div className="relative h-[500px] w-full overflow-hidden rounded-2xl bg-white shadow-2xl border border-gray-100">
        {productImages.map((image, index) => (
          <motion.div
            key={index}
            className={`absolute inset-0 transition-all duration-1000 ${
              index === currentIndex ? 'opacity-100 scale-100' : 'opacity-0 scale-105'
            }`}
            initial={{ opacity: 0, scale: 1.05 }}
            animate={{ 
              opacity: index === currentIndex ? 1 : 0,
              scale: index === currentIndex ? 1 : 1.05
            }}
            transition={{ duration: 1 }}
          >
            <img
              src={image.src}
              alt={image.title}
              className="w-full h-full object-contain bg-gray-50"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            <div className="absolute bottom-6 left-6 text-white">
              <h3 className="text-lg font-semibold mb-1">{image.title}</h3>
              <p className="text-sm text-white/90">{image.description}</p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Navigation Dots */}
      <div className="flex justify-center mt-4 space-x-2">
        {productImages.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentIndex 
                ? 'bg-blue-600 w-8' 
                : 'bg-gray-300 hover:bg-blue-400'
            }`}
          />
        ))}
      </div>

      {/* Floating Badges */}
      <motion.div 
        animate={{ y: [-5, 5, -5] }}
        transition={{ duration: 3, repeat: Infinity }}
        className="absolute -top-4 -right-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg z-10"
      >
        Live Demo
      </motion.div>

      {/* Success Metrics Card - Similar to reference image */}
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.5 }}
        className="absolute -bottom-6 -left-6 bg-white rounded-2xl p-4 shadow-xl border border-gray-100 z-10"
      >
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">100%</div>
            <div className="text-xs text-gray-600">Success Rate</div>
          </div>
          <div className="w-px h-10 bg-gray-200"></div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">Top</div>
            <div className="text-xs text-gray-600">Performance</div>
          </div>
        </div>
        
        {/* Start Success Journey Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="mt-3 w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-sm font-semibold py-2 px-4 rounded-lg hover:shadow-lg transition-all duration-200"
          onClick={() => {
            try {
              const element = document.getElementById('contact');
              if (element) {
                const navHeight = 64;
                const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
                const offsetPosition = elementPosition - navHeight;
                
                window.scrollTo({
                  top: offsetPosition,
                  behavior: 'smooth'
                });
              }
            } catch (error) {
              console.error('Error scrolling to contact:', error);
            }
          }}
        >
          Start Your Success Journey
        </motion.button>
      </motion.div>
    </div>
  );
};

export default function HeroSection() {
  const scrollToSection = (sectionId: string) => {
    try {
      const element = document.getElementById(sectionId);
      if (element) {
        const navHeight = 80; // Height of fixed navigation
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - navHeight;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });

        console.log(`Scrolling to section: ${sectionId}`);
      } else {
        console.warn(`Element with id '${sectionId}' not found`);
      }
    } catch (error) {
      console.error('Error scrolling to section:', error);
    }
  };

  const handleBookDemo = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Book Demo clicked');
    analytics.navigation.ctaClick('hero_book_demo');
    analytics.business.demoRequest();
    scrollToSection('contact');
  };

  const handleLearnMore = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Learn More clicked');
    analytics.navigation.ctaClick('hero_watch_preview');
    analytics.engagement.scrollToSection('features');
    scrollToSection('features');
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden scroll-mt-16">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[600px]">
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6 flex flex-col justify-center h-full"
          >
            {/* Badge */}
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium w-fit"
            >
              <Star className="w-4 h-4 mr-2 fill-current" />
              Trusted by 900+ Educational Institutions
            </motion.div>

            {/* Main Headline */}
            <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight">
              Empower Your Organization with
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
                Next-Gen Assessments
              </span>
            </h1>

            {/* Subheadline */}
            <p className="text-lg text-gray-600 leading-relaxed max-w-xl">
              SPOT (Student Proficiency Online Test) - The revolutionary assessment platform launching soon. Built on MinervaInfo's 25+ years of EdTech excellence, ready to transform how organizations conduct professional evaluations.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.button
                onClick={handleBookDemo}
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 text-lg cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Book a Free Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </motion.button>

              <motion.button
                onClick={handleLearnMore}
                type="button"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center justify-center border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-3 rounded-xl font-semibold transition-all duration-300 text-lg bg-white hover:bg-blue-50 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Preview
              </motion.button>
            </div>

            {/* Feature List */}
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Streamlined Assessments
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Enhanced Performance
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Institutional Growth
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Secure Platform
              </div>
            </div>
          </motion.div>
          
          {/* Right Side - Scrolling Product Images */}
          <motion.div 
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <ScrollingImages />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
