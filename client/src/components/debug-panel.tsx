import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Copy, Trash2, RefreshCw } from 'lucide-react';
import { errorReporter, type ErrorReport } from '@/lib/error-reporter';
import { toast } from '@/hooks/use-toast';

interface SystemStatus {
  timestamp: string;
  service: string;
  environment: string;
  checks: {
    database: { status: string; details: any };
    email: { status: string; details: any };
    turnstile: { status: string; details: any };
    environment: { status: string; details: any };
  };
}

export function DebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [errorReports, setErrorReports] = useState<ErrorReport[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadErrorReports();
      loadSystemStatus();
    }
  }, [isOpen]);

  const loadErrorReports = () => {
    const reports = errorReporter.getStoredErrors();
    setErrorReports(reports);
  };

  const loadSystemStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/system-status');
      const status = await response.json();
      setSystemStatus(status);
    } catch (error) {
      console.error('Failed to load system status:', error);
      toast({
        title: "Error",
        description: "Failed to load system status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const clearErrorReports = () => {
    errorReporter.clearStoredErrors();
    setErrorReports([]);
    toast({
      title: "Success",
      description: "Error reports cleared",
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "Copied",
        description: "Content copied to clipboard",
      });
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'configured':
        return 'default';
      case 'unhealthy':
      case 'error':
      case 'missing_variables':
        return 'destructive';
      case 'not_configured':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Only show in development or when explicitly enabled
  if (process.env.NODE_ENV === 'production' && !localStorage.getItem('debug_mode')) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isOpen ? (
        <Button
          onClick={() => setIsOpen(true)}
          variant="outline"
          size="sm"
          className="bg-background/80 backdrop-blur-sm"
        >
          Debug
        </Button>
      ) : (
        <Card className="w-96 max-h-96 overflow-hidden bg-background/95 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Debug Panel</CardTitle>
              <Button
                onClick={() => setIsOpen(false)}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 max-h-80 overflow-y-auto">
            {/* System Status */}
            <Collapsible>
              <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium">
                <ChevronRight className="h-4 w-4" />
                System Status
                <Button
                  onClick={loadSystemStatus}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 ml-auto"
                  disabled={loading}
                >
                  <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {systemStatus ? (
                  <div className="space-y-2">
                    {Object.entries(systemStatus.checks).map(([key, check]) => (
                      <div key={key} className="flex items-center justify-between text-xs">
                        <span className="capitalize">{key}:</span>
                        <Badge variant={getStatusBadgeVariant(check.status)}>
                          {check.status}
                        </Badge>
                      </div>
                    ))}
                    <Button
                      onClick={() => copyToClipboard(JSON.stringify(systemStatus, null, 2))}
                      variant="outline"
                      size="sm"
                      className="w-full h-6 text-xs"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy Status
                    </Button>
                  </div>
                ) : (
                  <div className="text-xs text-muted-foreground">Click refresh to load status</div>
                )}
              </CollapsibleContent>
            </Collapsible>

            {/* Error Reports */}
            <Collapsible>
              <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium">
                <ChevronRight className="h-4 w-4" />
                Error Reports ({errorReports.length})
                <Button
                  onClick={clearErrorReports}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 ml-auto"
                  disabled={errorReports.length === 0}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {errorReports.length > 0 ? (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {errorReports.slice(-5).reverse().map((report, index) => (
                      <div key={index} className="border rounded p-2 text-xs">
                        <div className="font-medium">{report.error.name}</div>
                        <div className="text-muted-foreground truncate">
                          {report.error.message}
                        </div>
                        <div className="text-muted-foreground">
                          {new Date(report.timestamp).toLocaleTimeString()}
                        </div>
                        <Button
                          onClick={() => copyToClipboard(JSON.stringify(report, null, 2))}
                          variant="outline"
                          size="sm"
                          className="w-full h-5 text-xs mt-1"
                        >
                          <Copy className="h-2 w-2 mr-1" />
                          Copy
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-xs text-muted-foreground">No error reports</div>
                )}
              </CollapsibleContent>
            </Collapsible>

            {/* System Info */}
            <Collapsible>
              <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium">
                <ChevronRight className="h-4 w-4" />
                System Info
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <Button
                  onClick={() => copyToClipboard(JSON.stringify(errorReporter.getSystemInfo(), null, 2))}
                  variant="outline"
                  size="sm"
                  className="w-full h-6 text-xs"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy System Info
                </Button>
              </CollapsibleContent>
            </Collapsible>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
