import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export default function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    try {
      const element = document.getElementById(sectionId);
      if (element) {
        const navHeight = 64;
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - navHeight;
        
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
        setIsMobileMenuOpen(false);
      } else {
        console.warn(`Element with id "${sectionId}" not found`);
      }
    } catch (error) {
      console.error('Error scrolling to section:', error);
    }
  };

  const handleBookDemo = () => {
    scrollToSection('contact');
  };

  const navLinks = [
    { href: 'home', label: 'Home', type: 'scroll' },
    { href: 'features', label: 'Features', type: 'scroll' },
    { href: 'benefits', label: 'Benefits', type: 'scroll' },
    { href: 'about', label: 'About', type: 'scroll' },
    { href: 'contact', label: 'Contact', type: 'scroll' }
  ];

  return (
    <nav className={`backdrop-blur-md sticky top-0 z-50 border-b transition-all duration-300 ${
      isScrolled
        ? 'bg-blue-600/95 border-blue-500 shadow-lg'
        : 'bg-white/95 border-gray-100 shadow-sm'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <button onClick={() => scrollToSection('home')} className="flex items-center">
              <div>
                <h1 className="text-3xl font-bold transition-colors">
                  <span className={isScrolled
                    ? 'text-white'
                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent'
                  }>
                    SPOT
                  </span>
                </h1>
                <p className={`text-sm transition-colors -mt-1 ${
                  isScrolled ? 'text-blue-100' : 'text-gray-500'
                }`}>by MinervaInfo</p>
              </div>
            </button>
          </div>
          
          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-8">
              {navLinks.map((link) => (
                <button
                  key={link.href}
                  onClick={() => scrollToSection(link.href)}
                  className={`px-3 py-2 text-sm font-medium transition-colors ${
                    isScrolled
                      ? 'text-blue-100 hover:text-white'
                      : 'text-gray-600 hover:text-blue-600'
                  }`}
                >
                  {link.label}
                </button>
              ))}
            </div>
          </div>
          
          <div className="hidden md:block">
            <Button
              onClick={handleBookDemo}
              className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-300 shadow-md hover:shadow-lg ${
                isScrolled
                  ? 'bg-white text-blue-600 hover:bg-blue-50'
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white'
              }`}
            >
              Start Free Trial
            </Button>
          </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`p-2 transition-colors ${
                isScrolled
                  ? 'text-blue-100 hover:text-white'
                  : 'text-gray-600 hover:text-blue-600'
              }`}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-gray-100"
          >
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navLinks.map((link) => (
                <button
                  key={link.href}
                  onClick={() => scrollToSection(link.href)}
                  className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 w-full text-left"
                >
                  {link.label}
                </button>
              ))}
              <div className="px-3 py-2">
                <Button 
                  onClick={handleBookDemo}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                >
                  Start Free Trial
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
