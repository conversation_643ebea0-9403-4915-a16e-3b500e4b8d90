import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Mail, MapPin, Clock, Calendar, Shield } from "lucide-react";
import { motion } from "framer-motion";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { analytics } from "@/lib/analytics";
import { errorReporter } from "@/lib/error-reporter";

const contactFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  institution: z.string().min(1, "Institution name is required"),
  studentCount: z.string().optional(),
  message: z.string().optional(),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

// Declare turnstile callback function globally
declare global {
  interface Window {
    turnstile: {
      render: (element: string | HTMLElement, options: {
        sitekey: string;
        callback?: (token: string) => void;
        'error-callback'?: () => void;
      }) => string;
      reset: (widgetId?: string) => void;
    };
  }
}

export default function ContactSection() {
  const { toast } = useToast();
  const turnstileRef = useRef<HTMLDivElement>(null);
  const [turnstileToken, setTurnstileToken] = useState<string>("");
  const [turnstileWidgetId, setTurnstileWidgetId] = useState<string>("");
  
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
  });

  // Initialize Turnstile when component mounts
  useEffect(() => {
    const initTurnstile = () => {
      if (window.turnstile && turnstileRef.current) {
        const widgetId = window.turnstile.render(turnstileRef.current, {
          sitekey: import.meta.env.VITE_TURNSTILE_SITE_KEY || "1x00000000000000000000AA",
          callback: (token: string) => {
            setTurnstileToken(token);
          },
          'error-callback': () => {
            setTurnstileToken("");
            toast({
              title: "Verification Error",
              description: "Please refresh the page and try again.",
              variant: "destructive",
            });
          },
        });
        setTurnstileWidgetId(widgetId);
      }
    };

    // Check if Turnstile is already loaded
    if (window.turnstile) {
      initTurnstile();
    } else {
      // Wait for Turnstile to load
      const checkTurnstile = setInterval(() => {
        if (window.turnstile) {
          initTurnstile();
          clearInterval(checkTurnstile);
        }
      }, 100);

      return () => clearInterval(checkTurnstile);
    }
  }, [toast]);

  const contactMutation = useMutation({
    mutationFn: async (data: ContactFormData & { turnstileToken: string }) => {
      const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2);
      console.log(`[${requestId}] Starting contact form submission`, {
        email: data.email,
        institution: data.institution,
        hasTurnstileToken: !!data.turnstileToken,
        turnstileTokenLength: data.turnstileToken?.length || 0,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });

      try {
        const response = await apiRequest('POST', '/api/contact', data);
        console.log(`[${requestId}] API request successful`, {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok
        });
        return response;
      } catch (error) {
        console.error(`[${requestId}] API request failed`, {
          error: error.message,
          stack: error.stack,
          name: error.name,
          status: error instanceof Response ? error.status : 'unknown',
          statusText: error instanceof Response ? error.statusText : 'unknown'
        });

        // Report error for debugging
        errorReporter.reportContactFormError(error, data);

        throw error;
      }
    },
    onSuccess: async (response) => {
      try {
        const result = await response.json();
        console.log('Contact form submission successful', {
          requestId: result.requestId,
          submissionId: result.id,
          message: result.message
        });

        analytics.contactForm.completed();
        toast({
          title: "Success!",
          description: result.message,
        });
        reset();
        setTurnstileToken("");
        // Reset Turnstile
        if (window.turnstile && turnstileWidgetId) {
          window.turnstile.reset(turnstileWidgetId);
        }
      } catch (error) {
        console.error('Error parsing success response:', error);
        // Still show success since the request went through
        toast({
          title: "Success!",
          description: "Thank you for your interest! We will contact you shortly.",
        });
      }
    },
    onError: async (error) => {
      // Enhanced error logging
      const errorDetails = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorType: error.constructor.name,
        errorMessage: error.message,
        stack: error.stack
      };

      console.error('Contact form submission error:', errorDetails);

      // Report error for debugging
      errorReporter.reportContactFormError(error, errorDetails.context);

      // Parse error message from response
      let errorMessage = "There was an error submitting your request. Please try again.";
      let requestId = null;

      if (error instanceof Response) {
        console.error('Response error details:', {
          status: error.status,
          statusText: error.statusText,
          ok: error.ok,
          type: error.type,
          url: error.url,
          headers: Object.fromEntries(error.headers.entries())
        });

        try {
          const errorData = await error.json();
          console.error('Error response body:', errorData);

          errorMessage = errorData.message || errorMessage;
          requestId = errorData.requestId;
          analytics.contactForm.failed(`api_error_${error.status}`);
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          analytics.contactForm.failed('api_error_unknown');
        }
      } else if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error('Network error detected:', error);
        errorMessage = "Network error. Please check your connection and try again.";
        analytics.contactForm.failed('network_error');
      } else {
        console.error('Unknown error type:', error);
        analytics.contactForm.failed('unknown_error');
      }

      // Include request ID in error message if available
      const displayMessage = requestId
        ? `${errorMessage} (Request ID: ${requestId})`
        : errorMessage;

      toast({
        title: "Error",
        description: displayMessage,
        variant: "destructive",
      });

      // Reset Turnstile on error
      if (window.turnstile && turnstileWidgetId) {
        window.turnstile.reset(turnstileWidgetId);
      }
      setTurnstileToken("");
    },
  });

  const onSubmit = (data: ContactFormData) => {
    if (!turnstileToken) {
      toast({
        title: "Verification Required",
        description: "Please complete the security verification.",
        variant: "destructive",
      });
      analytics.contactForm.failed('turnstile_verification_missing');
      return;
    }

    analytics.contactForm.started();
    contactMutation.mutate({ ...data, turnstileToken });
  };

  return (
    <section id="contact" className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden scroll-mt-16">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="font-bold text-4xl mb-6">
              Ready to Transform Your Assessment Process?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join hundreds of educational institutions that trust SPOT for their online assessment needs. Schedule a personalized demo today.
            </p>
            
            <div className="space-y-6">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="flex items-center space-x-4"
              >
                <div className="bg-blue-600 p-3 rounded-lg">
                  <Mail className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="font-semibold text-lg">Email Us</div>
                  <div className="text-blue-100"><EMAIL></div>
                </div>
              </motion.div>
              
              {/* India Offices */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-600 p-3 rounded-lg">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div className="font-semibold text-lg">India Offices</div>
                </div>
                
                <div className="ml-16 space-y-4">
                  <div>
                    <div className="font-medium text-blue-300 mb-1">Shimla</div>
                    <div className="text-blue-100 text-sm leading-relaxed">
                      Mehlog House, Boileauganj,<br />
                      Shimla-171005, India
                    </div>
                  </div>
                  
                  <div>
                    <div className="font-medium text-blue-300 mb-1">Noida</div>
                    <div className="text-blue-100 text-sm leading-relaxed">
                      603 Aristo HT, Supertech,<br />
                      Sector 34, Noida, India
                    </div>
                  </div>
                </div>
              </motion.div>
              
              {/* US Office */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-600 p-3 rounded-lg">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div className="font-semibold text-lg">US Office</div>
                </div>
                
                <div className="ml-16">
                  <div className="text-blue-100 text-sm leading-relaxed">
                    1683, Cambridge ST APT 3,<br />
                    Cambridge, MA, US
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-8">
                <h3 className="font-semibold text-2xl mb-6 text-center text-white">
                  Book Your Free Demo
                </h3>
                
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-white">First Name *</Label>
                      <Input
                        id="firstName"
                        {...register('firstName')}
                        className="bg-white/20 border-white/30 text-white placeholder:text-blue-200 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your first name"
                      />
                      {errors.firstName && (
                        <p className="text-red-300 text-sm mt-1">{errors.firstName.message}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-white">Last Name *</Label>
                      <Input
                        id="lastName"
                        {...register('lastName')}
                        className="bg-white/20 border-white/30 text-white placeholder:text-blue-200 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your last name"
                      />
                      {errors.lastName && (
                        <p className="text-red-300 text-sm mt-1">{errors.lastName.message}</p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-white">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...register('email')}
                      className="bg-white/20 border-white/30 text-white placeholder:text-blue-200 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your email address"
                    />
                    {errors.email && (
                      <p className="text-red-300 text-sm mt-1">{errors.email.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="phone" className="text-white">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      {...register('phone')}
                      className="bg-white/20 border-white/30 text-white placeholder:text-blue-200 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your phone number"
                    />
                    {errors.phone && (
                      <p className="text-red-300 text-sm mt-1">{errors.phone.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="institution" className="text-white">Institution Name *</Label>
                    <Input
                      id="institution"
                      {...register('institution')}
                      className="bg-white/20 border-white/30 text-white placeholder:text-blue-200 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your institution name"
                    />
                    {errors.institution && (
                      <p className="text-red-300 text-sm mt-1">{errors.institution.message}</p>
                    )}
                  </div>
                  

                  
                  <div>
                    <Label htmlFor="message" className="text-white">Additional Requirements</Label>
                    <Textarea
                      id="message"
                      {...register('message')}
                      rows={4}
                      className="bg-white/20 border-white/30 text-white placeholder:text-blue-200 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Tell us about your specific requirements..."
                    />
                  </div>
                  
                  {/* Cloudflare Turnstile */}
                  <div className="flex justify-center">
                    <div className="bg-white/10 p-4 rounded-lg border border-white/20">
                      <div className="flex items-center justify-center mb-2">
                        <Shield className="h-4 w-4 text-blue-300 mr-2" />
                        <span className="text-sm text-blue-100">Security Verification</span>
                      </div>
                      <div ref={turnstileRef} className="flex justify-center"></div>
                    </div>
                  </div>
                  
                  <Button
                    type="submit"
                    disabled={contactMutation.isPending || !turnstileToken}
                    className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    <Calendar className="mr-2 h-5 w-5" />
                    {contactMutation.isPending ? 'Booking Demo...' : 'Book Free Demo'}
                  </Button>
                  
                  <p className="text-sm text-blue-200 text-center">
                    By submitting this form, you agree to our Privacy Policy and Terms of Service.
                  </p>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
