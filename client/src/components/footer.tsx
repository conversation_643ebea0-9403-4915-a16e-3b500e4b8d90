import { Linkedin, Youtube, Instagram, Mail, Phone, MapPin } from "lucide-react";
import { <PERSON> } from "wouter";
import { motion } from "framer-motion";
import { analytics } from "@/lib/analytics";

export default function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const quickLinks = [
    { href: 'home', label: 'Home' },
    { href: 'features', label: 'Features' },
    { href: 'benefits', label: 'Benefits' },
    { href: 'about', label: 'About Us' },
    { href: 'contact', label: 'Contact' }
  ];

  const resources = [
    'Documentation',
    'API Reference',
    'Help Center',
    'Training Materials',
    'Best Practices'
  ];

  const solutions = [
    'Online Testing Platform',
    'Analytics Dashboard',
    'Question Bank Management',
    'Custom Content Creation',
    'Enterprise Integration'
  ];

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 opacity-90"></div>
      
      <div className="relative">
        {/* Main footer content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Column */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div>
                <h3 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
                  SPOT
                </h3>
                <p className="text-blue-300 text-sm font-medium">by MinervaInfo</p>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Revolutionizing online assessments for educational institutions across India with cutting-edge technology and comprehensive analytics.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center text-gray-300">
                  <Mail className="h-4 w-4 mr-3 text-blue-400" />
                  <a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors">
                    <EMAIL>
                  </a>
                </div>

              </div>

              {/* Social Links */}
              <div className="flex space-x-4">
                <a
                  href="https://www.linkedin.com/company/minervainfo/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-gray-800 rounded-lg text-gray-300 hover:text-blue-400 hover:bg-gray-700 transition-all duration-300"
                  aria-label="LinkedIn"
                  onClick={() => analytics.navigation.socialClick('linkedin')}
                >
                  <Linkedin className="h-5 w-5" />
                </a>
                <a
                  href="https://www.youtube.com/@minervainfoedtech"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-gray-800 rounded-lg text-gray-300 hover:text-red-400 hover:bg-gray-700 transition-all duration-300"
                  aria-label="YouTube"
                  onClick={() => analytics.navigation.socialClick('youtube')}
                >
                  <Youtube className="h-5 w-5" />
                </a>
                <a
                  href="https://www.instagram.com/minervainfo_in"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-gray-800 rounded-lg text-gray-300 hover:text-pink-400 hover:bg-gray-700 transition-all duration-300"
                  aria-label="Instagram"
                  onClick={() => analytics.navigation.socialClick('instagram')}
                >
                  <Instagram className="h-5 w-5" />
                </a>
              </div>
            </motion.div>
            
            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h4 className="font-bold text-lg mb-6 text-white">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.href}>
                    <button 
                      onClick={() => scrollToSection(link.href)}
                      className="text-gray-300 hover:text-blue-400 transition-colors text-left group flex items-center"
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-200">
                        {link.label}
                      </span>
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>
            
            {/* Resources */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="font-bold text-lg mb-6 text-white">Resources</h4>
              <ul className="space-y-3">
                {resources.map((item) => (
                  <li key={item}>
                    <span className="text-gray-500 cursor-not-allowed group flex items-center">
                      <span className="transition-transform duration-200">
                        {item}
                      </span>
                      <span className="ml-2 text-xs text-gray-600">(Coming Soon)</span>
                    </span>
                  </li>
                ))}
              </ul>
            </motion.div>
            
            {/* Solutions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h4 className="font-bold text-lg mb-6 text-white">Solutions</h4>
              <ul className="space-y-3">
                {solutions.map((solution) => (
                  <li key={solution}>
                    <span className="text-gray-500 cursor-not-allowed group flex items-center">
                      <span className="transition-transform duration-200">
                        {solution}
                      </span>
                      <span className="ml-2 text-xs text-gray-600">(Coming Soon)</span>
                    </span>
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>
        
        {/* Bottom bar */}
        <div className="border-t border-gray-700 bg-gray-800 bg-opacity-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
                <p className="text-gray-400 text-sm">
                  © 2025 MinervaInfo. All rights reserved.
                </p>
                <div className="flex space-x-6 text-sm">
                  <Link
                    href="/privacy-policy"
                    className="text-gray-400 hover:text-blue-400 transition-colors"
                    onClick={() => analytics.navigation.menuClick('privacy_policy')}
                  >
                    Privacy Policy
                  </Link>
                  <Link
                    href="/terms-of-service"
                    className="text-gray-400 hover:text-blue-400 transition-colors"
                    onClick={() => analytics.navigation.menuClick('terms_of_service')}
                  >
                    Terms of Service
                  </Link>
                  <Link
                    href="/cookie-policy"
                    className="text-gray-400 hover:text-blue-400 transition-colors"
                    onClick={() => analytics.navigation.menuClick('cookie_policy')}
                  >
                    Cookie Policy
                  </Link>
                </div>
              </div>
              
              <div className="text-sm text-gray-400">
                <span>Powered by MinervaInfo</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
