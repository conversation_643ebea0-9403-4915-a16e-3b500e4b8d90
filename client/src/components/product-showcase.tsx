import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

const showcaseImages = [
  {
    src: "/images/SPOT_TEASER_SS/Dashboard.jpg",
    title: "Admin Dashboard",
    description: "Comprehensive control panel for managing exams, students, and analytics with real-time insights.",
    category: "Administration"
  },
  {
    src: "/images/SPOT_TEASER_SS/Login_form.jpg",
    title: "Secure Authentication",
    description: "Multi-level security with role-based access control for administrators, teachers, and students.",
    category: "Security"
  },
  {
    src: "/images/SPOT_TEASER_SS/student_dashboard.jpg",
    title: "Student Portal",
    description: "Intuitive interface for students to access exams, view results, and track their progress.",
    category: "Student Experience"
  },
  {
    src: "/images/SPOT_TEASER_SS/exam_start_window.jpg",
    title: "Exam Interface",
    description: "Clean, distraction-free exam environment with timer, navigation, and auto-save features.",
    category: "Assessment"
  },
  {
    src: "/images/SPOT_TEASER_SS/question_panel.jpg",
    title: "Question Navigation",
    description: "Advanced question panel with color-coded status, bookmarking, and quick navigation.",
    category: "Assessment"
  },
  {
    src: "/images/SPOT_TEASER_SS/exam_paper_builder.jpg",
    title: "Exam Builder",
    description: "Powerful tool for creating custom exams with flexible question selection and difficulty levels.",
    category: "Content Creation"
  },
  {
    src: "/images/SPOT_TEASER_SS/Question_entry.jpg",
    title: "Question Management",
    description: "Comprehensive question bank with rich text editor, media support, and categorization.",
    category: "Content Creation"
  },
  {
    src: "/images/SPOT_TEASER_SS/subject_master.jpg",
    title: "Subject Organization",
    description: "Hierarchical subject management system supporting multi-level categorization.",
    category: "Administration"
  },
  {
    src: "/images/SPOT_TEASER_SS/Exam_master.jpg",
    title: "Exam Configuration",
    description: "Advanced exam settings with scheduling, duration, attempts, and security configurations.",
    category: "Administration"
  },
  {
    src: "/images/SPOT_TEASER_SS/review_and_submit.jpg",
    title: "Review & Submit",
    description: "Final review interface allowing students to check answers before submission.",
    category: "Assessment"
  },
  {
    src: "/images/SPOT_TEASER_SS/candidate_performance_report.jpg",
    title: "Performance Analytics",
    description: "Detailed performance reports with subject-wise analysis and comparative statistics.",
    category: "Analytics"
  },
  {
    src: "/images/SPOT_TEASER_SS/view_result_shows_answer_sheet.jpg",
    title: "Answer Sheet Review",
    description: "Comprehensive answer sheet with correct answers, explanations, and performance breakdown.",
    category: "Analytics"
  }
];

export default function ProductShowcase() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % showcaseImages.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % showcaseImages.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + showcaseImages.length) % showcaseImages.length);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Experience SPOT in Action
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Explore our comprehensive assessment platform through real screenshots. 
            See how SPOT transforms the way educational institutions manage and conduct online examinations.
          </p>
        </motion.div>

        {/* Main Carousel */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="relative"
        >
          {/* Image Container */}
          <div className="relative h-[500px] md:h-[600px] overflow-hidden rounded-2xl bg-white shadow-2xl border border-gray-100">
            {showcaseImages.map((image, index) => (
              <motion.div
                key={index}
                className={`absolute inset-0 ${
                  index === currentIndex ? 'opacity-100' : 'opacity-0'
                }`}
                initial={{ opacity: 0, scale: 1.1 }}
                animate={{ 
                  opacity: index === currentIndex ? 1 : 0,
                  scale: index === currentIndex ? 1 : 1.1
                }}
                transition={{ duration: 0.8 }}
              >
                <img
                  src={image.src}
                  alt={image.title}
                  className="w-full h-full object-contain bg-gray-50"
                />
                
                {/* Overlay with Info */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-8 left-8 right-8 text-white">
                    <div className="inline-block px-3 py-1 bg-blue-600 rounded-full text-sm font-medium mb-3">
                      {image.category}
                    </div>
                    <h3 className="text-2xl font-bold mb-2">{image.title}</h3>
                    <p className="text-lg text-white/90 leading-relaxed">{image.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
          >
            <ChevronRight className="w-6 h-6" />
          </button>

          {/* Current Image Info */}
          <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-4 max-w-md">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                {showcaseImages[currentIndex].category}
              </span>
              <span className="text-sm text-gray-500">
                {currentIndex + 1} / {showcaseImages.length}
              </span>
            </div>
            <h3 className="font-bold text-gray-900 mb-1">
              {showcaseImages[currentIndex].title}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2">
              {showcaseImages[currentIndex].description}
            </p>
          </div>
        </motion.div>

        {/* Thumbnail Navigation */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-8"
        >
          <div className="flex justify-center">
            <div className="flex space-x-2 overflow-x-auto max-w-full pb-2">
              {showcaseImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                    index === currentIndex 
                      ? 'border-blue-600 scale-110 shadow-lg' 
                      : 'border-gray-200 hover:border-blue-400 opacity-70 hover:opacity-100'
                  }`}
                >
                  <img
                    src={image.src}
                    alt={image.title}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Auto-play Control */}
        <div className="flex justify-center mt-6">
          <button
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              isAutoPlaying 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {isAutoPlaying ? 'Pause Auto-play' : 'Resume Auto-play'}
          </button>
        </div>
      </div>
    </section>
  );
}