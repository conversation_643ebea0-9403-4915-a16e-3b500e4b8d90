import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  Mail,
  Phone,
  MessageCircle,
  ChevronUp,
  Share2
} from "lucide-react";
import { analytics } from "@/lib/analytics";

const socialLinks = [
  {
    name: "LinkedIn",
    icon: Linkedin,
    url: "https://www.linkedin.com/company/minervainfo/",
    color: "bg-blue-600 hover:bg-blue-700",
    delay: 0.1
  },
  {
    name: "YouTube",
    icon: Youtube,
    url: "https://www.youtube.com/@minervainfoedtech",
    color: "bg-red-600 hover:bg-red-700",
    delay: 0.2
  },
  {
    name: "Instagram",
    icon: Instagram,
    url: "https://www.instagram.com/minervainfo_in",
    color: "bg-pink-600 hover:bg-pink-700",
    delay: 0.3
  }
];

const quickActions = [
  {
    name: "Email Us",
    icon: Mail,
    action: () => {
      analytics.navigation.socialClick('email');
      window.open("mailto:<EMAIL>");
    },
    color: "bg-green-600 hover:bg-green-700",
    delay: 0.4
  }
];

export default function FloatingSocialIcons() {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
        setIsExpanded(false);
      }

      if (window.pageYOffset > 500) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  const handleShare = async () => {
    analytics.navigation.socialClick('share');
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'SPOT - Student Proficiency Online Test',
          text: 'Check out SPOT - Advanced online assessment platform for educational institutions',
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <div className="fixed right-6 bottom-6 z-50 flex flex-col items-end space-y-3">
          {/* Scroll to Top Button */}
          <AnimatePresence>
            {showScrollTop && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 20 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={scrollToTop}
                className="bg-gray-800 hover:bg-gray-900 text-white p-3 rounded-full shadow-lg transition-all duration-300"
                title="Scroll to top"
              >
                <ChevronUp className="h-5 w-5" />
              </motion.button>
            )}
          </AnimatePresence>

          {/* Social Icons */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="flex flex-col space-y-2"
              >
                {/* Quick Actions */}
                {quickActions.map((action, index) => (
                  <motion.button
                    key={action.name}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 50 }}
                    transition={{ delay: action.delay }}
                    whileHover={{ scale: 1.1, x: -5 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={action.action}
                    className={`${action.color} text-white p-3 rounded-full shadow-lg transition-all duration-300 group relative`}
                    title={action.name}
                  >
                    <action.icon className="h-5 w-5" />
                    
                    {/* Tooltip */}
                    <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                      {action.name}
                    </div>
                  </motion.button>
                ))}

                {/* Divider */}
                <div className="w-8 h-px bg-gray-300 mx-auto my-2"></div>

                {/* Social Media Links */}
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 50 }}
                    transition={{ delay: social.delay }}
                    whileHover={{ scale: 1.1, x: -5 }}
                    whileTap={{ scale: 0.9 }}
                    className={`${social.color} text-white p-3 rounded-full shadow-lg transition-all duration-300 group relative`}
                    title={social.name}
                  >
                    <social.icon className="h-5 w-5" />
                    
                    {/* Tooltip */}
                    <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                      {social.name}
                    </div>
                  </motion.a>
                ))}

                {/* Share Button */}
                <motion.button
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.1, x: -5 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleShare}
                  className="bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 group relative"
                  title="Share this page"
                >
                  <Share2 className="h-5 w-5" />

                  {/* Tooltip */}
                  <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                    Share
                  </div>
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Toggle Button */}
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setIsExpanded(!isExpanded)}
            className={`${
              isExpanded 
                ? 'bg-red-500 hover:bg-red-600 rotate-45' 
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white p-4 rounded-full shadow-lg transition-all duration-300 relative group`}
            title={isExpanded ? "Close" : "Connect with us"}
          >
            <div className={`transition-transform duration-300 ${isExpanded ? 'rotate-45' : ''}`}>
              {isExpanded ? (
                <div className="h-6 w-6 relative">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-0.5 bg-white"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-white"></div>
                </div>
              ) : (
                <Share2 className="h-6 w-6" />
              )}
            </div>

            {/* Pulse Animation */}
            {!isExpanded && (
              <div className="absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20"></div>
            )}

            {/* Tooltip for main button */}
            {!isExpanded && (
              <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                Connect with us
              </div>
            )}
          </motion.button>
        </div>
      )}
    </AnimatePresence>
  );
}
