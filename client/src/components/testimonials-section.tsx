import { GraduationCap, Users, Globe, Award } from "lucide-react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function TestimonialsSection() {
  const capabilities = [
    {
      icon: GraduationCap,
      title: "Competitive Exam Excellence",
      description: "Specialized support for JEE Main, JEE Advanced, NEET, UPSC, and state-level competitive examinations with comprehensive question banks.",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Users,
      title: "Multi-Institution Support",
      description: "Designed for coaching institutes, schools, and universities with scalable architecture supporting concurrent assessments.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Globe,
      title: "India-Focused Platform",
      description: "Built specifically for the Indian education system with regional language support and curriculum alignment.",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Award,
      title: "Assessment Innovation",
      description: "Advanced proctoring, adaptive testing, and real-time performance analytics for comprehensive evaluation.",
      color: "from-orange-500 to-red-500"
    }
  ];

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-1/4 w-96 h-96 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
      <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Why Choose SPOT for
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
              Online Assessment?
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Experience next-generation online testing platform designed specifically for Indian educational institutions and competitive exam preparation.
          </p>
        </motion.div>
        
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {capabilities.map((capability, index) => (
            <motion.div
              key={capability.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Card className="h-full bg-white border border-gray-100 hover:border-blue-200 hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${capability.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <capability.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                    {capability.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {capability.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
        
        {/* SEO-Focused Content */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-white rounded-2xl p-12 shadow-lg border border-gray-100"
        >
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Transform Your Assessment Strategy
              </h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                SPOT provides comprehensive online testing solutions for educational institutions across India. Our platform supports JEE preparation, NEET coaching, UPSC training, and custom institutional assessments with advanced analytics and secure proctoring.
              </p>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  Question bank management for competitive exams
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  Real-time performance analytics and reporting
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  Secure online proctoring and anti-cheating measures
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  Scalable infrastructure for large-scale assessments
                </li>
              </ul>
            </div>
            <div className="bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl p-8 text-white">
              <h4 className="text-2xl font-bold mb-4">Ready to Get Started?</h4>
              <p className="mb-6 text-blue-100">
                Join educational institutions using SPOT for their online assessment needs. Request a demo to see our platform in action.
              </p>
              <Button 
                onClick={scrollToContact}
                className="bg-white text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg font-semibold transition-colors w-full"
              >
                Request Demo
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
